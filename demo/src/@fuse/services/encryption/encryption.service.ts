// src/app/services/encryption.service.ts
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';

@Injectable({ providedIn: 'root' })
export class EncryptionService {
  private publicKey: CryptoKey | null = null;
  private privateKey: CryptoKey | null = null;

  constructor(private http: HttpClient) {}

  /**
   * Generate an RSA-OAEP key pair in-browser.
   */
  async generateAsymmetricKeyPair(): Promise<void> {
    const keyPair = await window.crypto.subtle.generateKey(
      {
        name: 'RSA-OAEP',
        modulusLength: 2048,
        publicExponent: new Uint8Array([0x01, 0x00, 0x01]),
        hash: 'SHA-256',
      },
      true, // extractable
      ['encrypt', 'decrypt']
    );
    this.publicKey = keyPair.publicKey;
    this.privateKey = keyPair.privateKey;
  }

  /**
   * Export the public key as PEM string for sharing/server storage.
   */
  async exportPublicKey(): Promise<string> {
    if (!this.publicKey) {
      throw new Error('Public key not generated');
    }
    const spki = await window.crypto.subtle.exportKey('spki', this.publicKey);
    const b64 = this.arrayBufferToBase64(spki);
    return this.wrapPem(b64, 'PUBLIC KEY');
  }

  /**
   * Import a PEM public key string.
   */
  async importPublicKey(pem: string): Promise<void> {
    const b64 = pem
      .replace(/-----BEGIN PUBLIC KEY-----/, '')
      .replace(/-----END PUBLIC KEY-----/, '')
      .replace(/\s+/g, '');
    const binary = this.base64ToArrayBuffer(b64);
    this.publicKey = await window.crypto.subtle.importKey(
      'spki',
      binary,
      { name: 'RSA-OAEP', hash: 'SHA-256' },
      true,
      ['encrypt']
    );
  }

  /**
   * Encrypt a UTF-8 string using the imported/generate public key.
   */
  async encryptWithPublicKey(message: string): Promise<ArrayBuffer> {
    if (!this.publicKey) {
      throw new Error('Public key not set');
    }
    const encoder = new TextEncoder();
    const data = encoder.encode(message);
    return await window.crypto.subtle.encrypt(
      { name: 'RSA-OAEP' },
      this.publicKey,
      data
    );
  }

  /**
   * Export and optionally send private key PEM to secure storage or backend.
   */
  async exportPrivateKey(): Promise<string> {
    if (!this.privateKey) {
      throw new Error('Private key not generated');
    }
    const pkcs8 = await window.crypto.subtle.exportKey('pkcs8', this.privateKey);
    const b64 = this.arrayBufferToBase64(pkcs8);
    return this.wrapPem(b64, 'PRIVATE KEY');
  }

  /** Helper: wrap base64 in PEM format */
  private wrapPem(b64: string, label: string): string {
    const lines = b64.match(/.{1,64}/g) || [];
    return `-----BEGIN ${label}-----\n${lines.join('\n')}\n-----END ${label}-----`;
  }

  private arrayBufferToBase64(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let b of bytes) binary += String.fromCharCode(b);
    return window.btoa(binary);
  }

  private base64ToArrayBuffer(b64: string): ArrayBuffer {
    const binary = window.atob(b64);
    const bytes = new Uint8Array(binary.length);
    for (let i = 0; i < binary.length; i++) {
      bytes[i] = binary.charCodeAt(i);
    }
    return bytes.buffer;
  }

  /**
   * Send exported keys to backend for storage.
   */
  saveKeysToServer(publicPem: string, privatePem: string) {
    return this.http.post('/api/keys/save', { publicPem, privatePem });
  }
}


// src/app/components/encryption/encryption.component.ts
import { Component } from '@angular/core';
import { EncryptionService } from '../../services/encryption.service';

@Component({
  selector: 'app-encryption',
  template: `
  <div class="p-4">
    <h2 class="text-xl font-bold mb-4">Key Management & Encryption</h2>
    <button (click)="generateKeys()" class="btn btn-primary mb-2">Generate Key Pair</button>
    <pre *ngIf="publicPem"><strong>Public Key:</strong><br>{{ publicPem }}</pre>
    <pre *ngIf="privatePem"><strong>Private Key:</strong><br>{{ privatePem }}</pre>

    <div *ngIf="publicPem" class="mt-4">
      <textarea [(ngModel)]="message" placeholder="Enter message" class="w-full p-2 border"></textarea>
      <button (click)="encrypt()" class="btn btn-secondary mt-2">Encrypt Message</button>
    </div>

    <pre *ngIf="encryptedHex" class="mt-4"><strong>Ciphertext (hex):</strong><br>{{ encryptedHex }}</pre>
  </div>
  `
})
export class EncryptionComponent {
  public publicPem: string | null = null;
  public privatePem: string | null = null;
  public message = '';
  public encryptedHex: string | null = null;

  constructor(private enc: EncryptionService) {}

  async generateKeys() {
    await this.enc.generateAsymmetricKeyPair();
    this.publicPem = await this.enc.exportPublicKey();
    this.privatePem = await this.enc.exportPrivateKey();
    // Optionally save to server
    this.enc.saveKeysToServer(this.publicPem, this.privatePem).subscribe();
  }

  async encrypt() {
    const buf = await this.enc.encryptWithPublicKey(this.message);
    this.encryptedHex = Array.from(new Uint8Array(buf))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');
  }
}
