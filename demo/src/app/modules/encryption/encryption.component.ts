// src/app/components/encryption/encryption.component.ts
import { Component } from '@angular/core';
import { EncryptionService } from '../../../@fuse/services/encryption/encryption.service';

@Component({
  selector: 'app-encryption',
  template: `
  <div class="p-4">
    <h2 class="text-xl font-bold mb-4">Key Management & Encryption</h2>
    <button (click)="generateKeys()" class="btn btn-primary mb-2">Generate Key Pair</button>
    <pre *ngIf="publicPem"><strong>Public Key:</strong><br>{{ publicPem }}</pre>
    <pre *ngIf="privatePem"><strong>Private Key:</strong><br>{{ privatePem }}</pre>

    <div *ngIf="publicPem" class="mt-4">
      <textarea [(ngModel)]="message" placeholder="Enter message" class="w-full p-2 border"></textarea>
      <button (click)="encrypt()" class="btn btn-secondary mt-2">Encrypt Message</button>
    </div>

    <pre *ngIf="encryptedHex" class="mt-4"><strong>Ciphertext (hex):</strong><br>{{ encryptedHex }}</pre>
  </div>
  `
})
export class EncryptionComponent {
  public publicPem: string | null = null;
  public privatePem: string | null = null;
  public message = '';
  public encryptedHex: string | null = null;

  constructor(private enc: EncryptionService) {}

  async generateKeys() {
    await this.enc.generateAsymmetricKeyPair();
    this.publicPem = await this.enc.exportPublicKey();
    this.privatePem = await this.enc.exportPrivateKey();
    // Optionally save to server
    this.enc.saveKeysToServer(this.publicPem, this.privatePem).subscribe();
  }

  async encrypt() {
    const buf = await this.enc.encryptWithPublicKey(this.message);
    this.encryptedHex = Array.from(new Uint8Array(buf))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');
  }
}
