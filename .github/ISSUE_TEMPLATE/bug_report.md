---
name: Bug report
about: Create a report to help us improve
title: '[BUG] '
labels: ['bug', 'needs-triage']
assignees: ''
---

## Bug Description
A clear and concise description of what the bug is.

## Steps to Reproduce
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

## Expected Behavior
A clear and concise description of what you expected to happen.

## Actual Behavior
A clear and concise description of what actually happened.

## Environment
- OS: [e.g. Ubuntu 22.04]
- Node.js version: [e.g. 18.17.0]
- NPM version: [e.g. 9.6.7]
- Browser: [e.g. Chrome 120.0]

## Error Messages/Logs
```
Paste any relevant error messages or logs here
```

## Screenshots
If applicable, add screenshots to help explain your problem.

## Additional Context
Add any other context about the problem here.

## Priority
- [ ] Low
- [ ] Medium  
- [ ] High
- [ ] Critical