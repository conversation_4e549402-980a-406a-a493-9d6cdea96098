---
name: User Story
about: Define functionality from user's perspective
title: '[USER STORY] As a [user type], I want [goal] so that [reason]'
labels: ['user-story', 'needs-estimation']
assignees: ''
---

## User Story
**As a** [type of user]  
**I want** [some goal or objective]  
**So that** [some reason or benefit]

## Acceptance Criteria
- [ ] Given [context], when [action], then [expected result]
- [ ] Given [context], when [action], then [expected result]
- [ ] Given [context], when [action], then [expected result]

## Definition of Done
- [ ] Feature implemented according to acceptance criteria
- [ ] Unit tests written and passing
- [ ] Integration tests written and passing
- [ ] E2E tests updated (if applicable)
- [ ] API documentation updated (if applicable)
- [ ] Code reviewed and approved
- [ ] Deployed to staging environment
- [ ] Tested by QA/Product Owner
- [ ] Performance impact assessed

## Technical Notes
- API endpoints to be created/modified:
- Database changes required:
- Frontend components affected:
- Third-party integrations:

## Dependencies
- Depends on: #[issue number]
- Blocks: #[issue number]

## Estimation
- Story Points: [1, 2, 3, 5, 8, 13, 21]
- Time Estimate: [hours/days]

## Additional Context
Include any wireframes, mockups, or additional context here.