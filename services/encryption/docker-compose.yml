version: '3.8'

services:
  encryption-service:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: fuse19-encryption-service
    ports:
      - "3001:3001"
      - "9090:9090"  # Metrics port
    environment:
      - NODE_ENV=production
      - PORT=3001
      - SERVICE_SECRET=${SERVICE_SECRET:-your-super-secret-service-key}
      - API_KEY_SECRET=${API_KEY_SECRET:-your-api-key-secret}
      - REDIS_URL=redis://redis:6379
      - RATE_LIMIT_WINDOW_MS=900000
      - RATE_LIMIT_MAX_REQUESTS=100
      - ALLOWED_ORIGINS=http://localhost:4200,http://localhost:5000,http://localhost:3000
      - LOG_LEVEL=info
      - KEY_STORAGE_PATH=/app/keys
      - KEY_ENCRYPTION_PASSWORD=${KEY_ENCRYPTION_PASSWORD:-your-key-encryption-password}
      - SERVICE_NAME=encryption-service
      - SERVICE_VERSION=1.0.0
      - HEALTH_CHECK_INTERVAL=30000
      - METRICS_ENABLED=true
      - METRICS_PORT=9090
    volumes:
      - encryption_keys:/app/keys
      - encryption_logs:/app/logs
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - encryption-network
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3001/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) }).on('error', () => process.exit(1))"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  redis:
    image: redis:7-alpine
    container_name: fuse19-encryption-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - encryption-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Optional: Nginx reverse proxy for load balancing
  nginx:
    image: nginx:alpine
    container_name: fuse19-encryption-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - encryption-service
    restart: unless-stopped
    networks:
      - encryption-network
    profiles:
      - with-proxy

volumes:
  encryption_keys:
    driver: local
  encryption_logs:
    driver: local
  redis_data:
    driver: local

networks:
  encryption-network:
    driver: bridge
