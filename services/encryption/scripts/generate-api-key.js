#!/usr/bin/env node

/**
 * <PERSON>ript to generate API keys for the encryption service
 * Usage: node scripts/generate-api-key.js [--count=5] [--prefix=fuse]
 */

require('dotenv').config();
const { generateApiKey } = require('../middleware/auth');

// Parse command line arguments
const args = process.argv.slice(2);
const options = {
  count: 1,
  prefix: 'fuse'
};

args.forEach(arg => {
  if (arg.startsWith('--count=')) {
    options.count = parseInt(arg.split('=')[1]) || 1;
  } else if (arg.startsWith('--prefix=')) {
    options.prefix = arg.split('=')[1] || 'fuse';
  } else if (arg === '--help' || arg === '-h') {
    console.log(`
Usage: node scripts/generate-api-key.js [options]

Options:
  --count=N     Generate N API keys (default: 1)
  --prefix=STR  Use custom prefix (default: fuse)
  --help, -h    Show this help message

Examples:
  node scripts/generate-api-key.js
  node scripts/generate-api-key.js --count=5
  node scripts/generate-api-key.js --prefix=myapp --count=3

Environment Variables:
  API_KEY_SECRET  Secret used for key generation (required)
    `);
    process.exit(0);
  }
});

// Validate environment
if (!process.env.API_KEY_SECRET) {
  console.error('❌ Error: API_KEY_SECRET environment variable is required');
  console.error('   Set it in your .env file or export it:');
  console.error('   export API_KEY_SECRET="your-secret-key"');
  process.exit(1);
}

// Generate API keys
console.log(`🔑 Generating ${options.count} API key(s) with prefix "${options.prefix}"...\n`);

for (let i = 0; i < options.count; i++) {
  try {
    const apiKey = generateApiKey();
    const keyNumber = options.count > 1 ? ` #${i + 1}` : '';
    
    console.log(`API Key${keyNumber}:`);
    console.log(`  Key: ${apiKey}`);
    console.log(`  Header: X-API-Key: ${apiKey}`);
    console.log(`  Curl: curl -H "X-API-Key: ${apiKey}" http://localhost:3001/health`);
    
    if (i < options.count - 1) {
      console.log('');
    }
  } catch (error) {
    console.error(`❌ Error generating API key #${i + 1}:`, error.message);
  }
}

console.log(`\n✅ Generated ${options.count} API key(s) successfully`);
console.log('\n⚠️  Important Security Notes:');
console.log('   • Store these keys securely');
console.log('   • Never commit them to version control');
console.log('   • Use environment variables in production');
console.log('   • Rotate keys regularly');
console.log('   • Monitor key usage in logs');
