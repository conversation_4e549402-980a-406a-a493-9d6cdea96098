#!/usr/bin/env node

/**
 * Setup script for Fuse19 Encryption Service
 * Initializes the service with proper configuration and security
 */

const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');
const readline = require('readline');

class EncryptionServiceSetup {
  constructor() {
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
  }

  async start() {
    console.log('\n🔐 Fuse19 Encryption Service Setup');
    console.log('===================================');
    console.log('Welcome! This setup will configure your encryption microservice.\n');

    try {
      await this.checkPrerequisites();
      await this.createDirectories();
      await this.generateSecrets();
      await this.createEnvFile();
      await this.showCompletionInfo();
    } catch (error) {
      console.error('❌ Setup failed:', error.message);
      process.exit(1);
    } finally {
      this.rl.close();
    }
  }

  async checkPrerequisites() {
    console.log('🔍 Checking prerequisites...');
    
    // Check Node.js version
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    
    if (majorVersion < 18) {
      throw new Error(`Node.js 18+ required. Current version: ${nodeVersion}`);
    }
    
    console.log(`✅ Node.js version: ${nodeVersion}`);
    
    // Check if setup already exists
    const envExists = await this.fileExists('.env');
    if (envExists) {
      const overwrite = await this.prompt('⚠️  .env file already exists. Overwrite? (y/n): ');
      if (overwrite.toLowerCase() !== 'y') {
        console.log('Setup cancelled.');
        process.exit(0);
      }
    }
  }

  async createDirectories() {
    console.log('\n📁 Creating directories...');
    
    const directories = ['keys', 'logs', 'scripts'];
    
    for (const dir of directories) {
      try {
        await fs.mkdir(dir, { recursive: true });
        console.log(`✅ Created: ${dir}/`);
      } catch (error) {
        if (error.code !== 'EEXIST') {
          throw error;
        }
        console.log(`✅ Exists: ${dir}/`);
      }
    }
  }

  async generateSecrets() {
    console.log('\n🔐 Generating security secrets...');
    
    this.secrets = {
      serviceSecret: crypto.randomBytes(32).toString('hex'),
      apiKeySecret: crypto.randomBytes(32).toString('hex'),
      keyEncryptionPassword: crypto.randomBytes(24).toString('base64')
    };
    
    console.log('✅ Generated service secret');
    console.log('✅ Generated API key secret');
    console.log('✅ Generated key encryption password');
  }

  async createEnvFile() {
    console.log('\n⚙️  Creating configuration...');
    
    const port = await this.prompt('Service port [3001]: ') || '3001';
    const environment = await this.prompt('Environment (development/production) [development]: ') || 'development';
    const logLevel = await this.prompt('Log level (debug/info/warn/error) [info]: ') || 'info';
    
    const envContent = `# Fuse19 Encryption Service Configuration
# Generated on ${new Date().toISOString()}

# Server Configuration
PORT=${port}
NODE_ENV=${environment}

# Service Security
SERVICE_SECRET=${this.secrets.serviceSecret}
API_KEY_SECRET=${this.secrets.apiKeySecret}

# Redis Configuration (optional - for rate limiting)
# REDIS_URL=redis://localhost:6379

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:4200,http://localhost:5000,http://localhost:3000

# Logging
LOG_LEVEL=${logLevel}
LOG_FILE=./logs/encryption-service.log

# Key Storage
KEY_STORAGE_PATH=./keys
KEY_ENCRYPTION_PASSWORD=${this.secrets.keyEncryptionPassword}

# Service Discovery
SERVICE_NAME=encryption-service
SERVICE_VERSION=1.0.0
HEALTH_CHECK_INTERVAL=30000

# Monitoring
METRICS_ENABLED=true
METRICS_PORT=9090

# Database (optional - for key metadata and audit logs)
# MONGO_URI=mongodb://localhost:27017/fuse19-encryption
`;

    await fs.writeFile('.env', envContent);
    console.log('✅ Created .env file');
  }

  async showCompletionInfo() {
    console.log('\n🎉 Setup Complete!');
    console.log('==================');
    console.log('\n📋 Next Steps:');
    console.log('1. Review the .env file and adjust settings as needed');
    console.log('2. Install dependencies: npm install');
    console.log('3. Start the service: npm start');
    console.log('4. Use the CLI: npm run cli');
    console.log('5. Generate API keys: npm run generate-keys');
    
    console.log('\n🔧 Available Commands:');
    console.log('• npm start          - Start the encryption service');
    console.log('• npm run dev        - Start in development mode');
    console.log('• npm run cli        - Open the management CLI');
    console.log('• npm run generate-keys - Generate API keys');
    console.log('• npm test           - Run tests');
    
    console.log('\n🌐 Service Endpoints (when running):');
    console.log(`• API Base: http://localhost:${process.env.PORT || 3001}/api`);
    console.log(`• Health Check: http://localhost:${process.env.PORT || 3001}/health`);
    console.log(`• API Docs: http://localhost:${process.env.PORT || 3001}/api-docs`);
    
    console.log('\n🔐 Security Notes:');
    console.log('• Your secrets have been generated and stored in .env');
    console.log('• Never commit .env to version control');
    console.log('• Use different secrets for each environment');
    console.log('• Regularly rotate API keys and secrets');
    console.log('• Monitor logs for suspicious activity');
    
    console.log('\n📚 Documentation:');
    console.log('• README.md - Service overview and usage');
    console.log('• API documentation available at /api-docs when running');
    console.log('• Use the CLI for interactive management');
    
    const startNow = await this.prompt('\n🚀 Start the service now? (y/n): ');
    if (startNow.toLowerCase() === 'y') {
      console.log('\n⏳ Starting encryption service...');
      const { spawn } = require('child_process');
      const service = spawn('npm', ['start'], { stdio: 'inherit' });
      
      process.on('SIGINT', () => {
        service.kill('SIGINT');
        process.exit(0);
      });
    } else {
      console.log('\n✅ Setup complete! Run "npm start" when ready.');
    }
  }

  async prompt(question) {
    return new Promise((resolve) => {
      this.rl.question(question, resolve);
    });
  }

  async fileExists(filePath) {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }
}

// Run setup if called directly
if (require.main === module) {
  const setup = new EncryptionServiceSetup();
  setup.start().catch(error => {
    console.error('Setup error:', error);
    process.exit(1);
  });
}

module.exports = EncryptionServiceSetup;
