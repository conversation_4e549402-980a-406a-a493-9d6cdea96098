const express = require('express');
const crypto = require('crypto');
const { body, validationResult } = require('express-validator');
const { RSAKeyManager } = require('../utils/crypto');
const { AppError, catchAsync } = require('../middleware/error');
const { trackEncryptionOperation } = require('../middleware/metrics');
const { createLogger } = require('../utils/logger');

const router = express.Router();
const logger = createLogger();
const rsaManager = new RSAKeyManager();

/**
 * Digital Signature Utilities
 */
class DigitalSignature {
  /**
   * Sign data with RSA private key
   */
  static signData(data, privateKeyPem, passphrase = null) {
    try {
      const privateKey = crypto.createPrivateKey({
        key: privateKeyPem,
        passphrase: passphrase || process.env.KEY_ENCRYPTION_PASSWORD
      });

      const signature = crypto.sign('sha256', Buffer.from(data, 'utf8'), {
        key: privateKey,
        padding: crypto.constants.RSA_PKCS1_PSS_PADDING,
        saltLength: crypto.constants.RSA_PSS_SALTLEN_DIGEST
      });

      return signature.toString('base64');
    } catch (error) {
      logger.error('Digital signing failed', error);
      throw new Error('Digital signing failed');
    }
  }

  /**
   * Verify signature with RSA public key
   */
  static verifySignature(data, signature, publicKeyPem) {
    try {
      const publicKey = crypto.createPublicKey(publicKeyPem);
      
      const isValid = crypto.verify('sha256', Buffer.from(data, 'utf8'), {
        key: publicKey,
        padding: crypto.constants.RSA_PKCS1_PSS_PADDING,
        saltLength: crypto.constants.RSA_PSS_SALTLEN_DIGEST
      }, Buffer.from(signature, 'base64'));

      return isValid;
    } catch (error) {
      logger.error('Signature verification failed', error);
      return false;
    }
  }

  /**
   * Create hash of data for signing
   */
  static hashData(data, algorithm = 'sha256') {
    return crypto.createHash(algorithm).update(data, 'utf8').digest('hex');
  }

  /**
   * Sign hash instead of raw data (more efficient for large data)
   */
  static signHash(hash, privateKeyPem, passphrase = null) {
    try {
      const privateKey = crypto.createPrivateKey({
        key: privateKeyPem,
        passphrase: passphrase || process.env.KEY_ENCRYPTION_PASSWORD
      });

      const signature = crypto.sign('sha256', Buffer.from(hash, 'hex'), {
        key: privateKey,
        padding: crypto.constants.RSA_PKCS1_PSS_PADDING,
        saltLength: crypto.constants.RSA_PSS_SALTLEN_DIGEST
      });

      return signature.toString('base64');
    } catch (error) {
      logger.error('Hash signing failed', error);
      throw new Error('Hash signing failed');
    }
  }

  /**
   * Verify hash signature
   */
  static verifyHashSignature(hash, signature, publicKeyPem) {
    try {
      const publicKey = crypto.createPublicKey(publicKeyPem);
      
      const isValid = crypto.verify('sha256', Buffer.from(hash, 'hex'), {
        key: publicKey,
        padding: crypto.constants.RSA_PKCS1_PSS_PADDING,
        saltLength: crypto.constants.RSA_PSS_SALTLEN_DIGEST
      }, Buffer.from(signature, 'base64'));

      return isValid;
    } catch (error) {
      logger.error('Hash signature verification failed', error);
      return false;
    }
  }
}

/**
 * @swagger
 * /api/signatures/sign:
 *   post:
 *     summary: Create digital signature for data
 *     tags: [Digital Signatures]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - data
 *             properties:
 *               data:
 *                 type: string
 *                 description: Data to sign
 *               privateKey:
 *                 type: string
 *                 description: PEM formatted private key
 *               keyId:
 *                 type: string
 *                 description: ID of stored key pair to use
 *               passphrase:
 *                 type: string
 *                 description: Private key passphrase
 *               hashOnly:
 *                 type: boolean
 *                 default: false
 *                 description: If true, hash the data first (recommended for large data)
 *     responses:
 *       200:
 *         description: Data signed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 signature:
 *                   type: string
 *                 hash:
 *                   type: string
 *                 algorithm:
 *                   type: string
 */
router.post('/sign', [
  body('data').notEmpty().withMessage('Data is required'),
  body('privateKey').optional(),
  body('keyId').optional(),
  body('passphrase').optional(),
  body('hashOnly').optional().isBoolean()
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    });
  }

  const { data, privateKey, keyId, passphrase, hashOnly = false } = req.body;

  if (!privateKey && !keyId) {
    throw new AppError('Either privateKey or keyId must be provided', 400, 'MISSING_KEY');
  }

  logger.info('Digital signing requested', { 
    dataLength: data.length, 
    hasPrivateKey: !!privateKey, 
    keyId,
    hashOnly
  });

  // Get private key
  let keyToUse = privateKey;
  if (keyId && !privateKey) {
    const keyPair = await rsaManager.loadKeyPair(keyId);
    keyToUse = keyPair.privateKey;
  }

  let signature;
  let hash = null;

  if (hashOnly) {
    // Hash the data first, then sign the hash
    hash = DigitalSignature.hashData(data);
    signature = DigitalSignature.signHash(hash, keyToUse, passphrase);
  } else {
    // Sign the data directly
    signature = DigitalSignature.signData(data, keyToUse, passphrase);
  }

  trackEncryptionOperation('sign');

  logger.info('Digital signing completed successfully', { keyId, hashOnly });
  res.json({
    success: true,
    signature,
    hash,
    algorithm: 'RSA-PSS-SHA256',
    keyId: keyId || null,
    timestamp: new Date().toISOString()
  });
}));

/**
 * @swagger
 * /api/signatures/verify:
 *   post:
 *     summary: Verify digital signature
 *     tags: [Digital Signatures]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - data
 *               - signature
 *             properties:
 *               data:
 *                 type: string
 *                 description: Original data that was signed
 *               signature:
 *                 type: string
 *                 description: Base64 encoded signature
 *               publicKey:
 *                 type: string
 *                 description: PEM formatted public key
 *               keyId:
 *                 type: string
 *                 description: ID of stored key pair to use
 *               hash:
 *                 type: string
 *                 description: If provided, verify hash signature instead of data signature
 */
router.post('/verify', [
  body('data').optional(),
  body('signature').notEmpty().withMessage('Signature is required'),
  body('publicKey').optional(),
  body('keyId').optional(),
  body('hash').optional()
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    });
  }

  const { data, signature, publicKey, keyId, hash } = req.body;

  if (!publicKey && !keyId) {
    throw new AppError('Either publicKey or keyId must be provided', 400, 'MISSING_KEY');
  }

  if (!data && !hash) {
    throw new AppError('Either data or hash must be provided', 400, 'MISSING_DATA');
  }

  logger.info('Signature verification requested', { 
    hasData: !!data,
    hasHash: !!hash,
    hasPublicKey: !!publicKey, 
    keyId
  });

  // Get public key
  let keyToUse = publicKey;
  if (keyId && !publicKey) {
    const keyPair = await rsaManager.loadKeyPair(keyId);
    keyToUse = keyPair.publicKey;
  }

  let isValid;
  let verificationHash = hash;

  if (hash) {
    // Verify hash signature
    isValid = DigitalSignature.verifyHashSignature(hash, signature, keyToUse);
  } else {
    // Verify data signature directly
    isValid = DigitalSignature.verifySignature(data, signature, keyToUse);
    // Also compute hash for response
    verificationHash = DigitalSignature.hashData(data);
  }

  trackEncryptionOperation('verify');

  logger.info('Signature verification completed', { keyId, isValid });
  res.json({
    success: true,
    isValid,
    hash: verificationHash,
    algorithm: 'RSA-PSS-SHA256',
    keyId: keyId || null,
    timestamp: new Date().toISOString()
  });
}));

/**
 * @swagger
 * /api/signatures/hash:
 *   post:
 *     summary: Generate hash of data
 *     tags: [Digital Signatures]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - data
 *             properties:
 *               data:
 *                 type: string
 *                 description: Data to hash
 *               algorithm:
 *                 type: string
 *                 enum: [sha256, sha384, sha512]
 *                 default: sha256
 */
router.post('/hash', [
  body('data').notEmpty().withMessage('Data is required'),
  body('algorithm').optional().isIn(['sha256', 'sha384', 'sha512']).withMessage('Algorithm must be sha256, sha384, or sha512')
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    });
  }

  const { data, algorithm = 'sha256' } = req.body;

  logger.info('Hash generation requested', { 
    dataLength: data.length, 
    algorithm 
  });

  const hash = DigitalSignature.hashData(data, algorithm);

  logger.info('Hash generation completed successfully', { algorithm });
  res.json({
    success: true,
    hash,
    algorithm: algorithm.toUpperCase(),
    dataLength: data.length,
    timestamp: new Date().toISOString()
  });
}));

/**
 * @swagger
 * /api/signatures/sign-hash:
 *   post:
 *     summary: Sign a pre-computed hash
 *     tags: [Digital Signatures]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - hash
 *             properties:
 *               hash:
 *                 type: string
 *                 description: Hex encoded hash to sign
 *               privateKey:
 *                 type: string
 *                 description: PEM formatted private key
 *               keyId:
 *                 type: string
 *                 description: ID of stored key pair to use
 *               passphrase:
 *                 type: string
 *                 description: Private key passphrase
 */
router.post('/sign-hash', [
  body('hash').notEmpty().withMessage('Hash is required').isHexadecimal().withMessage('Hash must be hexadecimal'),
  body('privateKey').optional(),
  body('keyId').optional(),
  body('passphrase').optional()
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    });
  }

  const { hash, privateKey, keyId, passphrase } = req.body;

  if (!privateKey && !keyId) {
    throw new AppError('Either privateKey or keyId must be provided', 400, 'MISSING_KEY');
  }

  logger.info('Hash signing requested', { 
    hashLength: hash.length, 
    hasPrivateKey: !!privateKey, 
    keyId
  });

  // Get private key
  let keyToUse = privateKey;
  if (keyId && !privateKey) {
    const keyPair = await rsaManager.loadKeyPair(keyId);
    keyToUse = keyPair.privateKey;
  }

  const signature = DigitalSignature.signHash(hash, keyToUse, passphrase);

  trackEncryptionOperation('sign');

  logger.info('Hash signing completed successfully', { keyId });
  res.json({
    success: true,
    signature,
    hash,
    algorithm: 'RSA-PSS-SHA256',
    keyId: keyId || null,
    timestamp: new Date().toISOString()
  });
}));

/**
 * @swagger
 * /api/signatures/verify-hash:
 *   post:
 *     summary: Verify signature of a hash
 *     tags: [Digital Signatures]
 *     security:
 *       - ApiKeyAuth: []
 */
router.post('/verify-hash', [
  body('hash').notEmpty().withMessage('Hash is required').isHexadecimal().withMessage('Hash must be hexadecimal'),
  body('signature').notEmpty().withMessage('Signature is required'),
  body('publicKey').optional(),
  body('keyId').optional()
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    });
  }

  const { hash, signature, publicKey, keyId } = req.body;

  if (!publicKey && !keyId) {
    throw new AppError('Either publicKey or keyId must be provided', 400, 'MISSING_KEY');
  }

  logger.info('Hash signature verification requested', { 
    hashLength: hash.length,
    hasPublicKey: !!publicKey, 
    keyId
  });

  // Get public key
  let keyToUse = publicKey;
  if (keyId && !publicKey) {
    const keyPair = await rsaManager.loadKeyPair(keyId);
    keyToUse = keyPair.publicKey;
  }

  const isValid = DigitalSignature.verifyHashSignature(hash, signature, keyToUse);

  trackEncryptionOperation('verify');

  logger.info('Hash signature verification completed', { keyId, isValid });
  res.json({
    success: true,
    isValid,
    hash,
    algorithm: 'RSA-PSS-SHA256',
    keyId: keyId || null,
    timestamp: new Date().toISOString()
  });
}));

module.exports = router;
