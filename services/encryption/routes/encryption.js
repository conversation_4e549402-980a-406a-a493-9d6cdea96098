const express = require('express');
const { body, validationResult } = require('express-validator');
const { RSAKeyManager, AESManager } = require('../utils/crypto');
const { AppError, catchAsync } = require('../middleware/error');
const { trackEncryptionOperation } = require('../middleware/metrics');
const { createLogger } = require('../utils/logger');

const router = express.Router();
const logger = createLogger();
const rsaManager = new RSAKeyManager();
const aesManager = new AESManager();

/**
 * @swagger
 * /api/encryption/rsa/encrypt:
 *   post:
 *     summary: Encrypt data using RSA public key
 *     tags: [RSA Encryption]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - data
 *             properties:
 *               data:
 *                 type: string
 *                 description: Data to encrypt (max 190 bytes for RSA-2048)
 *               publicKey:
 *                 type: string
 *                 description: PEM formatted public key
 *               keyId:
 *                 type: string
 *                 description: ID of stored key pair to use
 *     responses:
 *       200:
 *         description: Data encrypted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 encryptedData:
 *                   type: string
 *                 algorithm:
 *                   type: string
 */
router.post('/rsa/encrypt', [
  body('data').notEmpty().withMessage('Data is required').isLength({ max: 190 }).withMessage('Data too large for RSA encryption (max 190 bytes)'),
  body('publicKey').optional(),
  body('keyId').optional()
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    });
  }

  const { data, publicKey, keyId } = req.body;

  if (!publicKey && !keyId) {
    throw new AppError('Either publicKey or keyId must be provided', 400, 'MISSING_KEY');
  }

  logger.info('RSA encryption requested', { 
    dataLength: data.length, 
    hasPublicKey: !!publicKey, 
    keyId 
  });

  let keyToUse = publicKey;
  if (keyId && !publicKey) {
    const keyPair = await rsaManager.loadKeyPair(keyId);
    keyToUse = keyPair.publicKey;
  }

  const encryptedData = rsaManager.encryptWithPublicKey(data, keyToUse);
  
  trackEncryptionOperation('encrypt');

  logger.info('RSA encryption completed successfully');
  res.json({
    success: true,
    encryptedData,
    algorithm: 'RSA-OAEP-SHA256',
    keyId: keyId || null
  });
}));

/**
 * @swagger
 * /api/encryption/rsa/decrypt:
 *   post:
 *     summary: Decrypt data using RSA private key
 *     tags: [RSA Encryption]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - encryptedData
 *             properties:
 *               encryptedData:
 *                 type: string
 *                 description: Base64 encoded encrypted data
 *               privateKey:
 *                 type: string
 *                 description: PEM formatted private key
 *               keyId:
 *                 type: string
 *                 description: ID of stored key pair to use
 *               passphrase:
 *                 type: string
 *                 description: Private key passphrase
 */
router.post('/rsa/decrypt', [
  body('encryptedData').notEmpty().withMessage('Encrypted data is required'),
  body('privateKey').optional(),
  body('keyId').optional(),
  body('passphrase').optional()
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    });
  }

  const { encryptedData, privateKey, keyId, passphrase } = req.body;

  if (!privateKey && !keyId) {
    throw new AppError('Either privateKey or keyId must be provided', 400, 'MISSING_KEY');
  }

  logger.info('RSA decryption requested', { 
    hasPrivateKey: !!privateKey, 
    keyId,
    hasPassphrase: !!passphrase
  });

  let keyToUse = privateKey;
  if (keyId && !privateKey) {
    const keyPair = await rsaManager.loadKeyPair(keyId);
    keyToUse = keyPair.privateKey;
  }

  const decryptedData = rsaManager.decryptWithPrivateKey(encryptedData, keyToUse, passphrase);
  
  trackEncryptionOperation('decrypt');

  logger.info('RSA decryption completed successfully');
  res.json({
    success: true,
    decryptedData,
    algorithm: 'RSA-OAEP-SHA256',
    keyId: keyId || null
  });
}));

/**
 * @swagger
 * /api/encryption/aes/generate-key:
 *   post:
 *     summary: Generate AES encryption key
 *     tags: [AES Encryption]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               keySize:
 *                 type: integer
 *                 enum: [128, 192, 256]
 *                 default: 256
 */
router.post('/aes/generate-key', [
  body('keySize').optional().isInt().isIn([128, 192, 256]).withMessage('Key size must be 128, 192, or 256')
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    });
  }

  const { keySize = 256 } = req.body;

  logger.info('AES key generation requested', { keySize });

  const key = aesManager.generateKey(keySize);
  
  trackEncryptionOperation('generateKeys');

  logger.info('AES key generated successfully', { keySize });
  res.json({
    success: true,
    key,
    keySize,
    algorithm: `AES-${keySize}-GCM`
  });
}));

/**
 * @swagger
 * /api/encryption/aes/encrypt:
 *   post:
 *     summary: Encrypt data using AES
 *     tags: [AES Encryption]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - data
 *               - key
 *             properties:
 *               data:
 *                 type: string
 *                 description: Data to encrypt
 *               key:
 *                 type: string
 *                 description: Base64 encoded AES key
 */
router.post('/aes/encrypt', [
  body('data').notEmpty().withMessage('Data is required'),
  body('key').notEmpty().withMessage('AES key is required')
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    });
  }

  const { data, key } = req.body;

  logger.info('AES encryption requested', { dataLength: data.length });

  const encryptedResult = aesManager.encrypt(data, key);
  
  trackEncryptionOperation('encrypt');

  logger.info('AES encryption completed successfully');
  res.json({
    success: true,
    ...encryptedResult,
    algorithm: 'AES-256-GCM'
  });
}));

/**
 * @swagger
 * /api/encryption/aes/decrypt:
 *   post:
 *     summary: Decrypt data using AES
 *     tags: [AES Encryption]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - encrypted
 *               - iv
 *               - authTag
 *               - key
 *             properties:
 *               encrypted:
 *                 type: string
 *                 description: Encrypted data (hex)
 *               iv:
 *                 type: string
 *                 description: Initialization vector (hex)
 *               authTag:
 *                 type: string
 *                 description: Authentication tag (hex)
 *               key:
 *                 type: string
 *                 description: Base64 encoded AES key
 */
router.post('/aes/decrypt', [
  body('encrypted').notEmpty().withMessage('Encrypted data is required'),
  body('iv').notEmpty().withMessage('IV is required'),
  body('authTag').notEmpty().withMessage('Auth tag is required'),
  body('key').notEmpty().withMessage('AES key is required')
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    });
  }

  const { encrypted, iv, authTag, key } = req.body;

  logger.info('AES decryption requested');

  const encryptedData = { encrypted, iv, authTag };
  const decryptedData = aesManager.decrypt(encryptedData, key);
  
  trackEncryptionOperation('decrypt');

  logger.info('AES decryption completed successfully');
  res.json({
    success: true,
    decryptedData,
    algorithm: 'AES-256-GCM'
  });
}));

/**
 * @swagger
 * /api/encryption/hybrid/encrypt:
 *   post:
 *     summary: Hybrid encryption (AES + RSA)
 *     description: Encrypts data with AES, then encrypts the AES key with RSA
 *     tags: [Hybrid Encryption]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - data
 *             properties:
 *               data:
 *                 type: string
 *                 description: Data to encrypt
 *               publicKey:
 *                 type: string
 *                 description: RSA public key for key encryption
 *               keyId:
 *                 type: string
 *                 description: ID of stored RSA key pair
 */
router.post('/hybrid/encrypt', [
  body('data').notEmpty().withMessage('Data is required'),
  body('publicKey').optional(),
  body('keyId').optional()
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    });
  }

  const { data, publicKey, keyId } = req.body;

  if (!publicKey && !keyId) {
    throw new AppError('Either publicKey or keyId must be provided', 400, 'MISSING_KEY');
  }

  logger.info('Hybrid encryption requested', { 
    dataLength: data.length, 
    hasPublicKey: !!publicKey, 
    keyId 
  });

  // Generate AES key
  const aesKey = aesManager.generateKey(256);
  
  // Encrypt data with AES
  const aesResult = aesManager.encrypt(data, aesKey);
  
  // Get RSA public key
  let rsaPublicKey = publicKey;
  if (keyId && !publicKey) {
    const keyPair = await rsaManager.loadKeyPair(keyId);
    rsaPublicKey = keyPair.publicKey;
  }
  
  // Encrypt AES key with RSA
  const encryptedAesKey = rsaManager.encryptWithPublicKey(aesKey, rsaPublicKey);
  
  trackEncryptionOperation('encrypt');

  logger.info('Hybrid encryption completed successfully');
  res.json({
    success: true,
    encryptedData: aesResult.encrypted,
    iv: aesResult.iv,
    authTag: aesResult.authTag,
    encryptedKey: encryptedAesKey,
    algorithm: 'AES-256-GCM + RSA-OAEP-SHA256',
    keyId: keyId || null
  });
}));

/**
 * @swagger
 * /api/encryption/hybrid/decrypt:
 *   post:
 *     summary: Hybrid decryption (RSA + AES)
 *     description: Decrypts the AES key with RSA, then decrypts data with AES
 *     tags: [Hybrid Encryption]
 *     security:
 *       - ApiKeyAuth: []
 */
router.post('/hybrid/decrypt', [
  body('encryptedData').notEmpty().withMessage('Encrypted data is required'),
  body('iv').notEmpty().withMessage('IV is required'),
  body('authTag').notEmpty().withMessage('Auth tag is required'),
  body('encryptedKey').notEmpty().withMessage('Encrypted AES key is required'),
  body('privateKey').optional(),
  body('keyId').optional(),
  body('passphrase').optional()
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    });
  }

  const { encryptedData, iv, authTag, encryptedKey, privateKey, keyId, passphrase } = req.body;

  if (!privateKey && !keyId) {
    throw new AppError('Either privateKey or keyId must be provided', 400, 'MISSING_KEY');
  }

  logger.info('Hybrid decryption requested', { 
    hasPrivateKey: !!privateKey, 
    keyId 
  });

  // Get RSA private key
  let rsaPrivateKey = privateKey;
  if (keyId && !privateKey) {
    const keyPair = await rsaManager.loadKeyPair(keyId);
    rsaPrivateKey = keyPair.privateKey;
  }
  
  // Decrypt AES key with RSA
  const aesKey = rsaManager.decryptWithPrivateKey(encryptedKey, rsaPrivateKey, passphrase);
  
  // Decrypt data with AES
  const aesEncryptedData = { encrypted: encryptedData, iv, authTag };
  const decryptedData = aesManager.decrypt(aesEncryptedData, aesKey);
  
  trackEncryptionOperation('decrypt');

  logger.info('Hybrid decryption completed successfully');
  res.json({
    success: true,
    decryptedData,
    algorithm: 'AES-256-GCM + RSA-OAEP-SHA256',
    keyId: keyId || null
  });
}));

module.exports = router;
