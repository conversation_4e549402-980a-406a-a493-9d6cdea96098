const express = require('express');
const { body, param, validationResult } = require('express-validator');
const { v4: uuidv4 } = require('uuid');
const { RSAKeyManager } = require('../utils/crypto');
const { AppError, catchAsync } = require('../middleware/error');
const { trackEncryptionOperation } = require('../middleware/metrics');
const { createLogger } = require('../utils/logger');

const router = express.Router();
const logger = createLogger();
const rsaManager = new RSAKeyManager();

/**
 * @swagger
 * /api/keys/generate:
 *   post:
 *     summary: Generate RSA key pair
 *     tags: [Key Management]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               keySize:
 *                 type: integer
 *                 enum: [1024, 2048, 3072, 4096]
 *                 default: 2048
 *               keyId:
 *                 type: string
 *                 description: Optional custom key ID
 *               saveToStorage:
 *                 type: boolean
 *                 default: false
 *     responses:
 *       200:
 *         description: Key pair generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 keyId:
 *                   type: string
 *                 publicKey:
 *                   type: string
 *                 privateKey:
 *                   type: string
 *                   description: Only returned if saveToStorage is false
 */
router.post('/generate', [
  body('keySize').optional().isInt({ min: 1024, max: 4096 }).withMessage('Key size must be between 1024 and 4096'),
  body('keyId').optional().isLength({ min: 1, max: 50 }).withMessage('Key ID must be 1-50 characters'),
  body('saveToStorage').optional().isBoolean().withMessage('saveToStorage must be boolean')
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    });
  }

  const { keySize = 2048, keyId = uuidv4(), saveToStorage = false } = req.body;

  logger.info('Generating RSA key pair', { keySize, keyId, saveToStorage });

  const { publicKey, privateKey } = await rsaManager.generateKeyPair(keySize);
  
  trackEncryptionOperation('generateKeys');

  let response = {
    success: true,
    keyId,
    publicKey
  };

  if (saveToStorage) {
    await rsaManager.saveKeyPair(keyId, publicKey, privateKey);
    response.message = 'Key pair generated and saved to secure storage';
  } else {
    response.privateKey = privateKey;
    response.message = 'Key pair generated successfully';
  }

  logger.info('RSA key pair generated successfully', { keyId, keySize });
  res.json(response);
}));

/**
 * @swagger
 * /api/keys/import:
 *   post:
 *     summary: Import RSA key pair
 *     tags: [Key Management]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - publicKey
 *             properties:
 *               keyId:
 *                 type: string
 *               publicKey:
 *                 type: string
 *               privateKey:
 *                 type: string
 *               passphrase:
 *                 type: string
 *               saveToStorage:
 *                 type: boolean
 *                 default: false
 */
router.post('/import', [
  body('keyId').optional().isLength({ min: 1, max: 50 }).withMessage('Key ID must be 1-50 characters'),
  body('publicKey').notEmpty().withMessage('Public key is required'),
  body('privateKey').optional(),
  body('passphrase').optional(),
  body('saveToStorage').optional().isBoolean()
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    });
  }

  const { keyId = uuidv4(), publicKey, privateKey, passphrase, saveToStorage = false } = req.body;

  logger.info('Importing RSA key pair', { keyId, hasPrivateKey: !!privateKey });

  // Validate and import public key
  const validPublicKey = rsaManager.importPublicKey(publicKey);
  let validPrivateKey = null;

  if (privateKey) {
    validPrivateKey = rsaManager.importPrivateKey(privateKey, passphrase);
    
    // Validate key pair integrity if both keys provided
    const isValid = await rsaManager.validateKeyPair(validPublicKey, validPrivateKey);
    if (!isValid) {
      throw new AppError('Key pair validation failed - keys do not match', 400, 'INVALID_KEY_PAIR');
    }
  }

  let response = {
    success: true,
    keyId,
    publicKey: validPublicKey
  };

  if (saveToStorage) {
    if (!validPrivateKey) {
      throw new AppError('Private key required for storage', 400, 'PRIVATE_KEY_REQUIRED');
    }
    await rsaManager.saveKeyPair(keyId, validPublicKey, validPrivateKey);
    response.message = 'Key pair imported and saved to secure storage';
  } else {
    if (validPrivateKey) {
      response.privateKey = validPrivateKey;
    }
    response.message = 'Key pair imported successfully';
  }

  logger.info('RSA key pair imported successfully', { keyId });
  res.json(response);
}));

/**
 * @swagger
 * /api/keys/{keyId}:
 *   get:
 *     summary: Get stored key pair
 *     tags: [Key Management]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: path
 *         name: keyId
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: includePrivateKey
 *         schema:
 *           type: boolean
 *           default: false
 */
router.get('/:keyId', [
  param('keyId').notEmpty().withMessage('Key ID is required')
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    });
  }

  const { keyId } = req.params;
  const { includePrivateKey = false } = req.query;

  logger.info('Retrieving key pair', { keyId, includePrivateKey });

  const { publicKey, privateKey } = await rsaManager.loadKeyPair(keyId);

  const response = {
    success: true,
    keyId,
    publicKey
  };

  if (includePrivateKey === 'true') {
    response.privateKey = privateKey;
  }

  logger.info('Key pair retrieved successfully', { keyId });
  res.json(response);
}));

/**
 * @swagger
 * /api/keys:
 *   get:
 *     summary: List all stored key pairs
 *     tags: [Key Management]
 *     security:
 *       - ApiKeyAuth: []
 */
router.get('/', catchAsync(async (req, res) => {
  logger.info('Listing stored key pairs');

  const keyIds = await rsaManager.listKeyPairs();

  res.json({
    success: true,
    keyIds,
    count: keyIds.length
  });
}));

/**
 * @swagger
 * /api/keys/{keyId}:
 *   delete:
 *     summary: Delete stored key pair
 *     tags: [Key Management]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: path
 *         name: keyId
 *         required: true
 *         schema:
 *           type: string
 */
router.delete('/:keyId', [
  param('keyId').notEmpty().withMessage('Key ID is required')
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    });
  }

  const { keyId } = req.params;

  logger.info('Deleting key pair', { keyId });

  await rsaManager.deleteKeyPair(keyId);

  logger.info('Key pair deleted successfully', { keyId });
  res.json({
    success: true,
    message: 'Key pair deleted successfully'
  });
}));

/**
 * @swagger
 * /api/keys/{keyId}/validate:
 *   post:
 *     summary: Validate key pair integrity
 *     tags: [Key Management]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: path
 *         name: keyId
 *         required: true
 *         schema:
 *           type: string
 */
router.post('/:keyId/validate', [
  param('keyId').notEmpty().withMessage('Key ID is required')
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    });
  }

  const { keyId } = req.params;

  logger.info('Validating key pair', { keyId });

  const { publicKey, privateKey } = await rsaManager.loadKeyPair(keyId);
  const isValid = await rsaManager.validateKeyPair(publicKey, privateKey);

  logger.info('Key pair validation completed', { keyId, isValid });
  res.json({
    success: true,
    keyId,
    isValid,
    message: isValid ? 'Key pair is valid' : 'Key pair validation failed'
  });
}));

module.exports = router;
