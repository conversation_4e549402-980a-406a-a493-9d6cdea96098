#!/usr/bin/env node

/**
 * Fuse19 Encryption Service CLI
 * Standalone command-line interface for managing the encryption microservice
 */

require('dotenv').config();
const readline = require('readline');
const { RSAKeyManager, AESManager } = require('./utils/crypto');
const { generateApiKey } = require('./middleware/auth');
const { createLogger } = require('./utils/logger');
const fs = require('fs').promises;
const path = require('path');

const logger = createLogger();
const rsaManager = new RSAKeyManager();
const aesManager = new AESManager();

// CLI Interface
class EncryptionCLI {
  constructor() {
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
  }

  async start() {
    console.log('\n🔐 Fuse19 Encryption Service CLI');
    console.log('==================================');
    console.log('Standalone encryption microservice management\n');
    
    await this.showMainMenu();
  }

  async showMainMenu() {
    console.log('\n📋 Main Menu:');
    console.log('1. 🔑 Key Management');
    console.log('2. 🔒 Encryption/Decryption');
    console.log('3. ✍️  Digital Signatures');
    console.log('4. 🔧 API Key Management');
    console.log('5. 📊 Service Status');
    console.log('6. ⚙️  Configuration');
    console.log('0. 🚪 Exit');
    
    const choice = await this.prompt('\nSelect an option: ');
    
    switch (choice) {
      case '1':
        await this.keyManagementMenu();
        break;
      case '2':
        await this.encryptionMenu();
        break;
      case '3':
        await this.signatureMenu();
        break;
      case '4':
        await this.apiKeyMenu();
        break;
      case '5':
        await this.statusMenu();
        break;
      case '6':
        await this.configMenu();
        break;
      case '0':
        await this.exit();
        return;
      default:
        console.log('❌ Invalid option. Please try again.');
        await this.showMainMenu();
    }
  }

  async keyManagementMenu() {
    console.log('\n🔑 Key Management:');
    console.log('1. Generate RSA Key Pair');
    console.log('2. List Stored Keys');
    console.log('3. Import Key Pair');
    console.log('4. Export Public Key');
    console.log('5. Delete Key Pair');
    console.log('6. Validate Key Pair');
    console.log('0. Back to Main Menu');
    
    const choice = await this.prompt('\nSelect an option: ');
    
    switch (choice) {
      case '1':
        await this.generateRSAKeyPair();
        break;
      case '2':
        await this.listStoredKeys();
        break;
      case '3':
        await this.importKeyPair();
        break;
      case '4':
        await this.exportPublicKey();
        break;
      case '5':
        await this.deleteKeyPair();
        break;
      case '6':
        await this.validateKeyPair();
        break;
      case '0':
        await this.showMainMenu();
        return;
      default:
        console.log('❌ Invalid option.');
        await this.keyManagementMenu();
    }
  }

  async encryptionMenu() {
    console.log('\n🔒 Encryption/Decryption:');
    console.log('1. RSA Encrypt');
    console.log('2. RSA Decrypt');
    console.log('3. AES Encrypt');
    console.log('4. AES Decrypt');
    console.log('5. Generate AES Key');
    console.log('6. Hybrid Encrypt (AES + RSA)');
    console.log('0. Back to Main Menu');
    
    const choice = await this.prompt('\nSelect an option: ');
    
    switch (choice) {
      case '1':
        await this.rsaEncrypt();
        break;
      case '2':
        await this.rsaDecrypt();
        break;
      case '3':
        await this.aesEncrypt();
        break;
      case '4':
        await this.aesDecrypt();
        break;
      case '5':
        await this.generateAESKey();
        break;
      case '6':
        await this.hybridEncrypt();
        break;
      case '0':
        await this.showMainMenu();
        return;
      default:
        console.log('❌ Invalid option.');
        await this.encryptionMenu();
    }
  }

  async signatureMenu() {
    console.log('\n✍️  Digital Signatures:');
    console.log('1. Sign Data');
    console.log('2. Verify Signature');
    console.log('3. Hash Data');
    console.log('0. Back to Main Menu');
    
    const choice = await this.prompt('\nSelect an option: ');
    
    switch (choice) {
      case '1':
        await this.signData();
        break;
      case '2':
        await this.verifySignature();
        break;
      case '3':
        await this.hashData();
        break;
      case '0':
        await this.showMainMenu();
        return;
      default:
        console.log('❌ Invalid option.');
        await this.signatureMenu();
    }
  }

  async apiKeyMenu() {
    console.log('\n🔧 API Key Management:');
    console.log('1. Generate API Key');
    console.log('2. Generate Multiple API Keys');
    console.log('3. Test API Key');
    console.log('0. Back to Main Menu');
    
    const choice = await this.prompt('\nSelect an option: ');
    
    switch (choice) {
      case '1':
        await this.generateSingleApiKey();
        break;
      case '2':
        await this.generateMultipleApiKeys();
        break;
      case '3':
        await this.testApiKey();
        break;
      case '0':
        await this.showMainMenu();
        return;
      default:
        console.log('❌ Invalid option.');
        await this.apiKeyMenu();
    }
  }

  async statusMenu() {
    console.log('\n📊 Service Status:');
    console.log('1. Service Health');
    console.log('2. Key Statistics');
    console.log('3. Storage Information');
    console.log('4. Environment Info');
    console.log('0. Back to Main Menu');
    
    const choice = await this.prompt('\nSelect an option: ');
    
    switch (choice) {
      case '1':
        await this.showServiceHealth();
        break;
      case '2':
        await this.showKeyStatistics();
        break;
      case '3':
        await this.showStorageInfo();
        break;
      case '4':
        await this.showEnvironmentInfo();
        break;
      case '0':
        await this.showMainMenu();
        return;
      default:
        console.log('❌ Invalid option.');
        await this.statusMenu();
    }
  }

  async configMenu() {
    console.log('\n⚙️  Configuration:');
    console.log('1. View Current Config');
    console.log('2. Set Key Storage Path');
    console.log('3. Set Log Level');
    console.log('4. Test Configuration');
    console.log('0. Back to Main Menu');
    
    const choice = await this.prompt('\nSelect an option: ');
    
    switch (choice) {
      case '1':
        await this.viewConfig();
        break;
      case '2':
        await this.setKeyStoragePath();
        break;
      case '3':
        await this.setLogLevel();
        break;
      case '4':
        await this.testConfiguration();
        break;
      case '0':
        await this.showMainMenu();
        return;
      default:
        console.log('❌ Invalid option.');
        await this.configMenu();
    }
  }

  // Key Management Methods
  async generateRSAKeyPair() {
    try {
      console.log('\n🔑 Generate RSA Key Pair');
      
      const keySize = await this.prompt('Key size (1024, 2048, 3072, 4096) [2048]: ') || '2048';
      const keyId = await this.prompt('Key ID (leave empty for auto-generated): ') || `rsa-${Date.now()}`;
      const save = await this.prompt('Save to storage? (y/n) [y]: ') || 'y';
      
      console.log('\n⏳ Generating key pair...');
      const { publicKey, privateKey } = await rsaManager.generateKeyPair(parseInt(keySize));
      
      if (save.toLowerCase() === 'y') {
        await rsaManager.saveKeyPair(keyId, publicKey, privateKey);
        console.log(`✅ Key pair generated and saved with ID: ${keyId}`);
      } else {
        console.log('✅ Key pair generated:');
        console.log('\n📄 Public Key:');
        console.log(publicKey);
        console.log('\n🔐 Private Key:');
        console.log(privateKey);
      }
      
      await this.prompt('\nPress Enter to continue...');
      await this.keyManagementMenu();
    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
      await this.prompt('Press Enter to continue...');
      await this.keyManagementMenu();
    }
  }

  async listStoredKeys() {
    try {
      console.log('\n📋 Stored Keys:');
      const keyIds = await rsaManager.listKeyPairs();
      
      if (keyIds.length === 0) {
        console.log('No keys found in storage.');
      } else {
        keyIds.forEach((keyId, index) => {
          console.log(`${index + 1}. ${keyId}`);
        });
      }
      
      await this.prompt('\nPress Enter to continue...');
      await this.keyManagementMenu();
    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
      await this.prompt('Press Enter to continue...');
      await this.keyManagementMenu();
    }
  }

  // API Key Methods
  async generateSingleApiKey() {
    try {
      console.log('\n🔧 Generate API Key');
      
      const apiKey = generateApiKey();
      
      console.log('✅ API Key generated:');
      console.log(`\n🔑 API Key: ${apiKey}`);
      console.log(`\n📋 Usage Examples:`);
      console.log(`   Header: X-API-Key: ${apiKey}`);
      console.log(`   Curl: curl -H "X-API-Key: ${apiKey}" http://localhost:3001/health`);
      
      console.log('\n⚠️  Security Notes:');
      console.log('   • Store this key securely');
      console.log('   • Never commit to version control');
      console.log('   • Use environment variables in production');
      
      await this.prompt('\nPress Enter to continue...');
      await this.apiKeyMenu();
    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
      await this.prompt('Press Enter to continue...');
      await this.apiKeyMenu();
    }
  }

  async generateMultipleApiKeys() {
    try {
      console.log('\n🔧 Generate Multiple API Keys');

      const count = parseInt(await this.prompt('Number of keys to generate [5]: ') || '5');

      console.log(`\n⏳ Generating ${count} API keys...\n`);

      for (let i = 0; i < count; i++) {
        const apiKey = generateApiKey();
        console.log(`${i + 1}. ${apiKey}`);
      }

      console.log('\n⚠️  Security Notes:');
      console.log('   • Store these keys securely');
      console.log('   • Distribute to authorized clients only');
      console.log('   • Monitor usage in logs');

      await this.prompt('\nPress Enter to continue...');
      await this.apiKeyMenu();
    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
      await this.prompt('Press Enter to continue...');
      await this.apiKeyMenu();
    }
  }

  async rsaEncrypt() {
    try {
      console.log('\n🔒 RSA Encryption');

      const data = await this.prompt('Enter data to encrypt: ');
      if (!data) {
        console.log('❌ No data provided');
        await this.prompt('Press Enter to continue...');
        await this.encryptionMenu();
        return;
      }

      const useStoredKey = await this.prompt('Use stored key? (y/n) [y]: ') || 'y';

      let publicKey;
      if (useStoredKey.toLowerCase() === 'y') {
        const keyIds = await rsaManager.listKeyPairs();
        if (keyIds.length === 0) {
          console.log('❌ No stored keys found');
          await this.prompt('Press Enter to continue...');
          await this.encryptionMenu();
          return;
        }

        console.log('\nAvailable keys:');
        keyIds.forEach((keyId, index) => {
          console.log(`${index + 1}. ${keyId}`);
        });

        const keyIndex = parseInt(await this.prompt('Select key (number): ')) - 1;
        if (keyIndex < 0 || keyIndex >= keyIds.length) {
          console.log('❌ Invalid key selection');
          await this.prompt('Press Enter to continue...');
          await this.encryptionMenu();
          return;
        }

        const keyPair = await rsaManager.loadKeyPair(keyIds[keyIndex]);
        publicKey = keyPair.publicKey;
      } else {
        publicKey = await this.prompt('Enter public key (PEM format): ');
      }

      console.log('\n⏳ Encrypting...');
      const encrypted = rsaManager.encryptWithPublicKey(data, publicKey);

      console.log('✅ Encryption successful:');
      console.log(`\n🔐 Encrypted Data (Base64):`);
      console.log(encrypted);

      await this.prompt('\nPress Enter to continue...');
      await this.encryptionMenu();
    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
      await this.prompt('Press Enter to continue...');
      await this.encryptionMenu();
    }
  }

  async generateAESKey() {
    try {
      console.log('\n🔑 Generate AES Key');

      const keySize = await this.prompt('Key size (128, 192, 256) [256]: ') || '256';

      console.log('\n⏳ Generating AES key...');
      const key = aesManager.generateKey(parseInt(keySize));

      console.log('✅ AES Key generated:');
      console.log(`\n🔑 Key (Base64): ${key}`);
      console.log(`\n⚠️  Store this key securely - it cannot be recovered if lost!`);

      await this.prompt('\nPress Enter to continue...');
      await this.encryptionMenu();
    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
      await this.prompt('Press Enter to continue...');
      await this.encryptionMenu();
    }
  }

  async showServiceHealth() {
    try {
      console.log('\n📊 Service Health Status');
      console.log('========================');

      console.log(`✅ Service: Running`);
      console.log(`🕐 Uptime: ${Math.floor(process.uptime())} seconds`);
      console.log(`💾 Memory Usage: ${Math.round(process.memoryUsage().heapUsed / 1024 / 1024)} MB`);
      console.log(`🆔 Process ID: ${process.pid}`);
      console.log(`📦 Node Version: ${process.version}`);
      console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);

      // Check key storage
      try {
        const keyIds = await rsaManager.listKeyPairs();
        console.log(`🔑 Stored Keys: ${keyIds.length}`);
      } catch (error) {
        console.log(`🔑 Key Storage: ❌ Error accessing storage`);
      }

      await this.prompt('\nPress Enter to continue...');
      await this.statusMenu();
    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
      await this.prompt('Press Enter to continue...');
      await this.statusMenu();
    }
  }

  async viewConfig() {
    try {
      console.log('\n⚙️  Current Configuration');
      console.log('========================');

      console.log(`🔧 Port: ${process.env.PORT || 3001}`);
      console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
      console.log(`📁 Key Storage: ${process.env.KEY_STORAGE_PATH || './keys'}`);
      console.log(`📝 Log Level: ${process.env.LOG_LEVEL || 'info'}`);
      console.log(`🔒 API Key Secret: ${process.env.API_KEY_SECRET ? '***SET***' : '❌ NOT SET'}`);
      console.log(`🔐 Key Encryption Password: ${process.env.KEY_ENCRYPTION_PASSWORD ? '***SET***' : '❌ NOT SET'}`);
      console.log(`⏱️  Rate Limit Window: ${process.env.RATE_LIMIT_WINDOW_MS || 900000}ms`);
      console.log(`🚦 Rate Limit Max: ${process.env.RATE_LIMIT_MAX_REQUESTS || 100} requests`);

      await this.prompt('\nPress Enter to continue...');
      await this.configMenu();
    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
      await this.prompt('Press Enter to continue...');
      await this.configMenu();
    }
  }

  // Utility Methods
  async prompt(question) {
    return new Promise((resolve) => {
      this.rl.question(question, resolve);
    });
  }

  async exit() {
    console.log('\n👋 Thank you for using Fuse19 Encryption Service CLI!');
    console.log('Service is ready to accept API requests on port 3001');
    console.log('\n📚 API Documentation: http://localhost:3001/api-docs');
    console.log('🏥 Health Check: http://localhost:3001/health');
    this.rl.close();
    process.exit(0);
  }
}

// Start CLI if run directly
if (require.main === module) {
  const cli = new EncryptionCLI();
  cli.start().catch(error => {
    console.error('❌ CLI Error:', error);
    process.exit(1);
  });
}

module.exports = EncryptionCLI;
