{"name": "fuse19-encryption-service", "version": "1.0.0", "description": "Encryption microservice for Fuse19 Backend - RSA/AES encryption, key management, and digital signatures", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "docker:build": "docker build -t fuse19-encryption .", "docker:run": "docker run -p 3001:3001 --env-file .env fuse19-encryption"}, "keywords": ["encryption", "rsa", "aes", "microservice", "crypto", "security", "digital-signatures"], "author": "Fuse19 Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.5.1", "express-validator": "^7.0.1", "dotenv": "^16.3.1", "winston": "^3.17.0", "uuid": "^9.0.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "redis": "^4.6.0", "bcrypt": "^5.1.1"}, "devDependencies": {"jest": "^29.7.0", "supertest": "^6.3.3", "nodemon": "^3.0.1"}, "engines": {"node": ">=18.0.0"}}