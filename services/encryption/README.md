# 🔐 Fuse19 Encryption Microservice

A standalone, production-ready encryption microservice providing RSA/AES encryption, key management, and digital signatures.

## ✨ Features

- **🔑 RSA Key Management**: Generate, import, export, and store RSA key pairs
- **🔒 RSA Encryption**: Encrypt/decrypt data using RSA-OAEP-SHA256
- **⚡ AES Encryption**: High-performance symmetric encryption with AES-256-GCM
- **🔄 Hybrid Encryption**: Combine AES + RSA for large data encryption
- **✍️ Digital Signatures**: Sign and verify data using RSA-PSS-SHA256
- **🔧 CLI Management**: Interactive text-based management interface
- **🛡️ Security**: Rate limiting, API key authentication, audit logging
- **📊 Monitoring**: Health checks, metrics, and comprehensive logging
- **🐳 Docker Ready**: Full containerization support
- **📚 API Documentation**: Auto-generated Swagger/OpenAPI docs

## 🚀 Quick Start

### 1. Setup

```bash
# Clone or navigate to the encryption service directory
cd services/encryption

# Run the interactive setup
npm run setup

# Or manual setup:
cp .env.example .env
# Edit .env with your configuration
```

### 2. Install Dependencies

```bash
npm install
```

### 3. Start the Service

```bash
# Production mode
npm start

# Development mode (with auto-reload)
npm run dev

# With Docker
docker-compose up --build
```

### 4. Use the CLI

```bash
# Open the interactive CLI
npm run cli
```

## 🔧 CLI Management

The service includes a comprehensive CLI for management:

```bash
npm run cli
```

### CLI Features:

- **🔑 Key Management**: Generate, list, import, export, delete, validate keys
- **🔒 Encryption/Decryption**: Interactive encryption operations
- **✍️ Digital Signatures**: Sign and verify data
- **🔧 API Key Management**: Generate and manage API keys
- **📊 Service Status**: Health checks and statistics
- **⚙️ Configuration**: View and modify settings

## 📡 API Usage

### Authentication

All API endpoints require authentication via API key:

```bash
# Generate API keys using CLI or script
npm run generate-keys

# Use in requests
curl -H "X-API-Key: your-api-key" http://localhost:3001/api/keys/generate
```

### Key Management

```bash
# Generate RSA key pair
curl -X POST http://localhost:3001/api/keys/generate \
  -H "X-API-Key: your-api-key" \
  -H "Content-Type: application/json" \
  -d '{"keySize": 2048, "saveToStorage": true}'

# List stored keys
curl -H "X-API-Key: your-api-key" http://localhost:3001/api/keys
```

### Encryption

```bash
# RSA Encryption
curl -X POST http://localhost:3001/api/encryption/rsa/encrypt \
  -H "X-API-Key: your-api-key" \
  -H "Content-Type: application/json" \
  -d '{"data": "Hello World", "keyId": "your-key-id"}'

# AES Encryption
curl -X POST http://localhost:3001/api/encryption/aes/encrypt \
  -H "X-API-Key: your-api-key" \
  -H "Content-Type: application/json" \
  -d '{"data": "Hello World", "key": "your-base64-aes-key"}'
```

### Digital Signatures

```bash
# Sign data
curl -X POST http://localhost:3001/api/signatures/sign \
  -H "X-API-Key: your-api-key" \
  -H "Content-Type: application/json" \
  -d '{"data": "Hello World", "keyId": "your-key-id"}'

# Verify signature
curl -X POST http://localhost:3001/api/signatures/verify \
  -H "X-API-Key: your-api-key" \
  -H "Content-Type: application/json" \
  -d '{"data": "Hello World", "signature": "signature-string", "keyId": "your-key-id"}'
```

## 🐳 Docker Deployment

### Development

```bash
# Start with development configuration
docker-compose -f docker-compose.dev.yml up --build
```

### Production

```bash
# Start with production configuration
docker-compose up -d --build

# With reverse proxy
docker-compose --profile with-proxy up -d --build
```

### Environment Variables

```env
# Server
PORT=3001
NODE_ENV=production

# Security
SERVICE_SECRET=your-service-secret
API_KEY_SECRET=your-api-key-secret
KEY_ENCRYPTION_PASSWORD=your-key-password

# Storage
KEY_STORAGE_PATH=/app/keys

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Monitoring
METRICS_ENABLED=true
LOG_LEVEL=info
```

## 🛡️ Security

### Key Storage
- Keys are encrypted at rest using AES-256-CBC
- Configurable key encryption passwords
- Secure file permissions (600)
- Optional HSM/Cloud KMS integration

### API Security
- API key authentication with HMAC signatures
- Rate limiting per client
- CORS protection
- Security headers (Helmet.js)
- Request/response logging

### Audit Trail
- All operations logged with timestamps
- Client identification and IP tracking
- Failed authentication attempts logged
- Key usage statistics

## 📊 Monitoring

### Health Checks

```bash
# Basic health
curl http://localhost:3001/health

# Detailed metrics
curl http://localhost:3001/health/detailed

# Readiness probe
curl http://localhost:3001/health/ready

# Liveness probe
curl http://localhost:3001/health/live
```

### Metrics

- Request counts and response times
- Error rates and types
- Key generation and usage statistics
- Memory and CPU usage
- Active connections

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `PORT` | Service port | `3001` |
| `NODE_ENV` | Environment | `development` |
| `SERVICE_SECRET` | Service encryption secret | Required |
| `API_KEY_SECRET` | API key signing secret | Required |
| `KEY_STORAGE_PATH` | Key storage directory | `./keys` |
| `KEY_ENCRYPTION_PASSWORD` | Key encryption password | Required |
| `LOG_LEVEL` | Logging level | `info` |
| `RATE_LIMIT_MAX_REQUESTS` | Rate limit per window | `100` |
| `RATE_LIMIT_WINDOW_MS` | Rate limit window | `900000` |

### Key Rotation

Keys should be rotated regularly:

1. Generate new key pairs
2. Update applications to use new keys
3. Deprecate old keys gradually
4. Revoke old keys after transition

## 🧪 Testing

```bash
# Run all tests
npm test

# Run with coverage
npm run test:coverage

# Watch mode
npm run test:watch
```

## 📚 API Documentation

When running, visit:
- **Swagger UI**: http://localhost:3001/api-docs
- **Health Check**: http://localhost:3001/health

## 🤝 Integration

### With Main Backend

```javascript
// Example integration in your main application
const axios = require('axios');

const encryptionService = axios.create({
  baseURL: 'http://localhost:3001/api',
  headers: {
    'X-API-Key': process.env.ENCRYPTION_API_KEY
  }
});

// Generate key pair
const keyPair = await encryptionService.post('/keys/generate', {
  keySize: 2048,
  saveToStorage: true
});

// Encrypt data
const encrypted = await encryptionService.post('/encryption/rsa/encrypt', {
  data: 'sensitive information',
  keyId: keyPair.data.keyId
});
```

## 🔍 Troubleshooting

### Common Issues

1. **Port already in use**: Change `PORT` in `.env`
2. **Permission denied**: Check file permissions on key storage directory
3. **API key invalid**: Regenerate API keys using CLI
4. **Memory issues**: Adjust Docker memory limits

### Logs

```bash
# View service logs
tail -f logs/encryption-service.log

# View error logs
tail -f logs/error.log

# Docker logs
docker-compose logs -f encryption-service
```

## 📄 License

MIT License - see LICENSE file for details.

## 🆘 Support

- Use the CLI for interactive help: `npm run cli`
- Check logs for detailed error information
- Review API documentation at `/api-docs`
- Ensure all environment variables are properly set
