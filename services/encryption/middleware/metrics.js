const { createLogger } = require('../utils/logger');

const logger = createLogger();

// Simple in-memory metrics store
// In production, use proper metrics collection (Prometheus, etc.)
const metrics = {
  requests: {
    total: 0,
    byEndpoint: {},
    byMethod: {},
    byStatus: {}
  },
  responseTime: {
    total: 0,
    count: 0,
    average: 0,
    min: Infinity,
    max: 0
  },
  errors: {
    total: 0,
    byType: {}
  },
  encryption: {
    operations: 0,
    keyGenerations: 0,
    signatures: 0
  }
};

/**
 * Middleware to collect basic metrics
 */
const metricsMiddleware = (req, res, next) => {
  const startTime = Date.now();
  
  // Increment request counter
  metrics.requests.total++;
  
  // Track by endpoint
  const endpoint = req.route?.path || req.path;
  metrics.requests.byEndpoint[endpoint] = (metrics.requests.byEndpoint[endpoint] || 0) + 1;
  
  // Track by method
  metrics.requests.byMethod[req.method] = (metrics.requests.byMethod[req.method] || 0) + 1;

  // Override res.end to capture response metrics
  const originalEnd = res.end;
  res.end = function(...args) {
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    
    // Update response time metrics
    metrics.responseTime.total += responseTime;
    metrics.responseTime.count++;
    metrics.responseTime.average = metrics.responseTime.total / metrics.responseTime.count;
    metrics.responseTime.min = Math.min(metrics.responseTime.min, responseTime);
    metrics.responseTime.max = Math.max(metrics.responseTime.max, responseTime);
    
    // Track by status code
    const statusCode = res.statusCode;
    const statusRange = `${Math.floor(statusCode / 100)}xx`;
    metrics.requests.byStatus[statusRange] = (metrics.requests.byStatus[statusRange] || 0) + 1;
    
    // Track errors
    if (statusCode >= 400) {
      metrics.errors.total++;
      metrics.errors.byType[statusRange] = (metrics.errors.byType[statusRange] || 0) + 1;
    }
    
    // Log request completion
    logger.info('Request completed', {
      method: req.method,
      url: req.url,
      statusCode,
      responseTime,
      ip: req.ip
    });
    
    originalEnd.apply(this, args);
  };
  
  next();
};

/**
 * Track encryption-specific operations
 */
const trackEncryptionOperation = (operation) => {
  switch (operation) {
    case 'encrypt':
    case 'decrypt':
      metrics.encryption.operations++;
      break;
    case 'generateKeys':
      metrics.encryption.keyGenerations++;
      break;
    case 'sign':
    case 'verify':
      metrics.encryption.signatures++;
      break;
  }
};

/**
 * Get current metrics
 */
const getMetrics = () => {
  return {
    ...metrics,
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    timestamp: new Date().toISOString()
  };
};

/**
 * Reset metrics (useful for testing)
 */
const resetMetrics = () => {
  metrics.requests.total = 0;
  metrics.requests.byEndpoint = {};
  metrics.requests.byMethod = {};
  metrics.requests.byStatus = {};
  
  metrics.responseTime.total = 0;
  metrics.responseTime.count = 0;
  metrics.responseTime.average = 0;
  metrics.responseTime.min = Infinity;
  metrics.responseTime.max = 0;
  
  metrics.errors.total = 0;
  metrics.errors.byType = {};
  
  metrics.encryption.operations = 0;
  metrics.encryption.keyGenerations = 0;
  metrics.encryption.signatures = 0;
};

/**
 * Health check function
 */
const getHealthStatus = () => {
  const uptime = process.uptime();
  const memory = process.memoryUsage();
  const errorRate = metrics.requests.total > 0 ? (metrics.errors.total / metrics.requests.total) * 100 : 0;
  
  return {
    status: errorRate < 5 ? 'healthy' : 'degraded', // Consider unhealthy if error rate > 5%
    uptime: Math.floor(uptime),
    memory: {
      used: Math.round(memory.heapUsed / 1024 / 1024), // MB
      total: Math.round(memory.heapTotal / 1024 / 1024), // MB
      external: Math.round(memory.external / 1024 / 1024) // MB
    },
    requests: {
      total: metrics.requests.total,
      errorRate: Math.round(errorRate * 100) / 100
    },
    averageResponseTime: Math.round(metrics.responseTime.average * 100) / 100,
    timestamp: new Date().toISOString()
  };
};

module.exports = {
  metricsMiddleware,
  trackEncryptionOperation,
  getMetrics,
  resetMetrics,
  getHealthStatus
};
