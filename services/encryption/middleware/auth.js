const crypto = require('crypto');
const { AppError } = require('./error');
const { createLogger } = require('../utils/logger');

const logger = createLogger();

/**
 * Simple API key authentication middleware
 * In production, this should be replaced with proper JWT validation
 * or integration with your main authentication service
 */
const authMiddleware = (req, res, next) => {
  try {
    const apiKey = req.headers['x-api-key'] || req.headers['authorization']?.replace('Bearer ', '');
    
    if (!apiKey) {
      return next(new AppError('API key is required', 401, 'MISSING_API_KEY'));
    }

    // Validate API key format and signature
    if (!isValidApiKey(apiKey)) {
      logger.warn('Invalid API key attempt', {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        url: req.url
      });
      return next(new AppError('Invalid API key', 401, 'INVALID_API_KEY'));
    }

    // Add API key info to request for logging
    req.apiKey = {
      key: apiKey.substring(0, 8) + '...',
      timestamp: Date.now()
    };

    logger.info('API key authenticated', {
      apiKey: req.apiKey.key,
      ip: req.ip,
      url: req.url,
      method: req.method
    });

    next();
  } catch (error) {
    logger.error('Authentication error', error);
    return next(new AppError('Authentication failed', 401, 'AUTH_ERROR'));
  }
};

/**
 * Validate API key format and signature
 * This is a simple implementation - in production, use proper key validation
 */
const isValidApiKey = (apiKey) => {
  try {
    // Expected format: fuse_<timestamp>_<signature>
    const parts = apiKey.split('_');
    if (parts.length !== 3 || parts[0] !== 'fuse') {
      return false;
    }

    const [prefix, timestamp, signature] = parts;
    
    // Check if timestamp is reasonable (not too old)
    const keyTimestamp = parseInt(timestamp, 36);
    const now = Date.now();
    const maxAge = 365 * 24 * 60 * 60 * 1000; // 1 year
    
    if (now - keyTimestamp > maxAge) {
      return false;
    }

    // Verify signature (simple HMAC check)
    const expectedSignature = crypto
      .createHmac('sha256', process.env.API_KEY_SECRET || 'default-secret')
      .update(`${prefix}_${timestamp}`)
      .digest('hex')
      .substring(0, 32);

    return crypto.timingSafeEqual(
      Buffer.from(signature, 'hex'),
      Buffer.from(expectedSignature, 'hex')
    );
  } catch (error) {
    return false;
  }
};

/**
 * Generate a new API key
 * This utility function can be used to create API keys for clients
 */
const generateApiKey = () => {
  const prefix = 'fuse';
  const timestamp = Date.now().toString(36);
  const signature = crypto
    .createHmac('sha256', process.env.API_KEY_SECRET || 'default-secret')
    .update(`${prefix}_${timestamp}`)
    .digest('hex')
    .substring(0, 32);

  return `${prefix}_${timestamp}_${signature}`;
};

/**
 * Optional: Role-based access control middleware
 */
const requireRole = (roles) => {
  return (req, res, next) => {
    // This would typically check user roles from JWT or database
    // For now, we'll assume all authenticated requests have access
    next();
  };
};

module.exports = {
  authMiddleware,
  generateApiKey,
  requireRole
};
