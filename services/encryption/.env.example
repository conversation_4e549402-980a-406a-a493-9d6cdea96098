# Encryption Service Configuration
PORT=3001
NODE_ENV=development

# Service Security
SERVICE_SECRET=your-super-secret-service-key-change-this-in-production
API_KEY_SECRET=your-api-key-secret-for-service-authentication

# Redis Configuration (for caching and rate limiting)
REDIS_URL=redis://localhost:6379

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:4200,http://localhost:5000,http://localhost:3000

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/encryption-service.log

# Key Storage (for production, use secure key management service)
KEY_STORAGE_PATH=./keys
KEY_ENCRYPTION_PASSWORD=your-key-encryption-password

# Service Discovery (if using service mesh)
SERVICE_NAME=encryption-service
SERVICE_VERSION=1.0.0
HEALTH_CHECK_INTERVAL=30000

# Monitoring
METRICS_ENABLED=true
METRICS_PORT=9090
