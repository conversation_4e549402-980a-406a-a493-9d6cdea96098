#!/usr/bin/env node

/**
 * Startup script for Fuse19 Encryption Service
 * Provides helpful information and starts the service
 */

require('dotenv').config();
const fs = require('fs');
const path = require('path');

// Check if setup has been run
function checkSetup() {
  const envExists = fs.existsSync('.env');
  const keysDir = fs.existsSync('keys');
  const logsDir = fs.existsSync('logs');
  
  if (!envExists) {
    console.log('⚠️  Setup required! Run: npm run setup');
    process.exit(1);
  }
  
  if (!process.env.API_KEY_SECRET) {
    console.log('⚠️  API_KEY_SECRET not configured. Run: npm run setup');
    process.exit(1);
  }
  
  return { envExists, keysDir, logsDir };
}

// Show startup information
function showStartupInfo() {
  const port = process.env.PORT || 3001;
  const env = process.env.NODE_ENV || 'development';
  
  console.log('\n🔐 Fuse19 Encryption Service');
  console.log('============================');
  console.log(`🌍 Environment: ${env}`);
  console.log(`🔧 Port: ${port}`);
  console.log(`📁 Key Storage: ${process.env.KEY_STORAGE_PATH || './keys'}`);
  console.log(`📝 Log Level: ${process.env.LOG_LEVEL || 'info'}`);
  
  console.log('\n🌐 Service Endpoints:');
  console.log(`• API Base: http://localhost:${port}/api`);
  console.log(`• Health Check: http://localhost:${port}/health`);
  console.log(`• API Documentation: http://localhost:${port}/api-docs`);
  
  console.log('\n🔧 Management:');
  console.log('• CLI: npm run cli');
  console.log('• Generate API Keys: npm run generate-keys');
  console.log('• View Logs: tail -f logs/encryption-service.log');
  
  console.log('\n🔑 Quick API Key Generation:');
  console.log('Run in another terminal: npm run generate-keys');
  
  console.log('\n⏳ Starting service...\n');
}

// Main startup
function main() {
  try {
    checkSetup();
    showStartupInfo();
    
    // Start the actual server
    require('./server.js');
  } catch (error) {
    console.error('❌ Startup failed:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { checkSetup, showStartupInfo };
