# 🚀 Getting Started with Fuse19 Encryption Service

This is a complete standalone encryption microservice that you can run independently of any UI or main application.

## 📋 What You Get

✅ **Complete Encryption Service**
- RSA key generation, import/export
- AES symmetric encryption
- Digital signatures
- Hybrid encryption (AES + RSA)

✅ **Text-Based CLI Management**
- Interactive menus for all operations
- No UI dependencies
- Easy key management
- Service monitoring

✅ **Production Ready**
- Docker containerization
- API authentication
- Rate limiting
- Comprehensive logging
- Health checks

✅ **RESTful API**
- Full REST API with Swagger docs
- Easy integration with any application
- Secure API key authentication

## 🏃‍♂️ Quick Start (5 minutes)

### 1. Navigate to the service directory
```bash
cd services/encryption
```

### 2. Run the interactive setup
```bash
npm run setup
```
This will:
- Create necessary directories
- Generate security secrets
- Create configuration file
- Guide you through initial setup

### 3. Install dependencies
```bash
npm install
```

### 4. Start the service
```bash
npm start
```

### 5. Open the CLI in another terminal
```bash
npm run cli
```

## 🎯 What to Do First

### 1. Generate API Keys
In the CLI, go to:
```
Main Menu → API Key Management → Generate API Key
```

Or use the command:
```bash
npm run generate-keys
```

### 2. Generate Your First RSA Key Pair
In the CLI:
```
Main Menu → Key Management → Generate RSA Key Pair
```

### 3. Test Encryption
In the CLI:
```
Main Menu → Encryption/Decryption → RSA Encrypt
```

## 🌐 Using the API

Once you have API keys, you can use the REST API:

### Generate Keys
```bash
curl -X POST http://localhost:3001/api/keys/generate \
  -H "X-API-Key: your-api-key" \
  -H "Content-Type: application/json" \
  -d '{"keySize": 2048, "saveToStorage": true}'
```

### Encrypt Data
```bash
curl -X POST http://localhost:3001/api/encryption/rsa/encrypt \
  -H "X-API-Key: your-api-key" \
  -H "Content-Type: application/json" \
  -d '{"data": "Hello World", "keyId": "your-key-id"}'
```

### View API Documentation
Visit: http://localhost:3001/api-docs

## 🐳 Docker Quick Start

### Development
```bash
docker-compose -f docker-compose.dev.yml up --build
```

### Production
```bash
docker-compose up --build
```

## 📁 Project Structure

```
services/encryption/
├── 📄 server.js              # Main server
├── 🖥️  cli.js                # Interactive CLI
├── ⚙️  start.js              # Startup script
├── 📋 package.json           # Dependencies
├── 🔧 .env.example           # Configuration template
├── 📚 README.md              # Full documentation
├── 🚀 GETTING_STARTED.md     # This file
├── 🐳 Dockerfile             # Container config
├── 🐳 docker-compose.yml     # Production deployment
├── 🐳 docker-compose.dev.yml # Development deployment
├── 📁 routes/                # API endpoints
│   ├── encryption.js         # Encryption operations
│   ├── keyManagement.js      # Key management
│   ├── signature.js          # Digital signatures
│   └── health.js             # Health checks
├── 📁 middleware/            # Express middleware
│   ├── auth.js               # API authentication
│   ├── error.js              # Error handling
│   └── metrics.js            # Monitoring
├── 📁 utils/                 # Utilities
│   ├── crypto.js             # Crypto operations
│   └── logger.js             # Logging
├── 📁 config/                # Configuration
│   └── swagger.js            # API documentation
└── 📁 scripts/               # Management scripts
    ├── setup.js              # Initial setup
    └── generate-api-key.js   # API key generation
```

## 🔧 Available Commands

| Command | Description |
|---------|-------------|
| `npm run setup` | Interactive setup wizard |
| `npm start` | Start the service |
| `npm run dev` | Start in development mode |
| `npm run cli` | Open management CLI |
| `npm run generate-keys` | Generate API keys |
| `npm test` | Run tests |
| `docker-compose up` | Start with Docker |

## 🔍 Service Endpoints

| Endpoint | Description |
|----------|-------------|
| `http://localhost:3001/health` | Health check |
| `http://localhost:3001/api-docs` | API documentation |
| `http://localhost:3001/api/keys/*` | Key management |
| `http://localhost:3001/api/encryption/*` | Encryption operations |
| `http://localhost:3001/api/signatures/*` | Digital signatures |

## 🛡️ Security Features

- ✅ API key authentication with HMAC signatures
- ✅ Rate limiting (100 requests per 15 minutes by default)
- ✅ Keys encrypted at rest with AES-256-CBC
- ✅ Secure file permissions (600)
- ✅ CORS protection
- ✅ Security headers (Helmet.js)
- ✅ Request/response logging
- ✅ Input validation and sanitization

## 🔧 Integration Examples

### Node.js
```javascript
const axios = require('axios');

const encryptionClient = axios.create({
  baseURL: 'http://localhost:3001/api',
  headers: { 'X-API-Key': 'your-api-key' }
});

// Generate key pair
const keyPair = await encryptionClient.post('/keys/generate', {
  keySize: 2048,
  saveToStorage: true
});

// Encrypt data
const encrypted = await encryptionClient.post('/encryption/rsa/encrypt', {
  data: 'sensitive data',
  keyId: keyPair.data.keyId
});
```

### Python
```python
import requests

headers = {'X-API-Key': 'your-api-key'}
base_url = 'http://localhost:3001/api'

# Generate key pair
response = requests.post(f'{base_url}/keys/generate', 
                        headers=headers,
                        json={'keySize': 2048, 'saveToStorage': True})
key_pair = response.json()

# Encrypt data
response = requests.post(f'{base_url}/encryption/rsa/encrypt',
                        headers=headers,
                        json={'data': 'sensitive data', 'keyId': key_pair['keyId']})
encrypted = response.json()
```

## 🆘 Need Help?

1. **Use the CLI**: `npm run cli` - Interactive help and operations
2. **Check logs**: `tail -f logs/encryption-service.log`
3. **API docs**: http://localhost:3001/api-docs
4. **Health check**: http://localhost:3001/health
5. **Read the full README.md** for detailed documentation

## 🎉 You're Ready!

Your encryption microservice is now ready to use. It's completely standalone and can be integrated with any application that needs encryption services.

**Next Steps:**
1. Generate some API keys
2. Create RSA key pairs
3. Test encryption operations
4. Integrate with your applications
5. Deploy with Docker for production

Happy encrypting! 🔐
