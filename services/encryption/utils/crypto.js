const crypto = require('crypto');
const fs = require('fs').promises;
const path = require('path');
const { createLogger } = require('./logger');

const logger = createLogger();

/**
 * RSA Key Management Utilities
 */
class RSAKeyManager {
  constructor() {
    this.keyStoragePath = process.env.KEY_STORAGE_PATH || path.join(__dirname, '../keys');
    this.keyEncryptionPassword = process.env.KEY_ENCRYPTION_PASSWORD || 'default-password';
    this.ensureKeyDirectory();
  }

  async ensureKeyDirectory() {
    try {
      await fs.mkdir(this.keyStoragePath, { recursive: true });
    } catch (error) {
      logger.error('Failed to create key storage directory', error);
    }
  }

  /**
   * Generate RSA key pair
   */
  generateKeyPair(keySize = 2048) {
    return new Promise((resolve, reject) => {
      crypto.generateKeyPair('rsa', {
        modulusLength: keySize,
        publicKeyEncoding: {
          type: 'spki',
          format: 'pem'
        },
        privateKeyEncoding: {
          type: 'pkcs8',
          format: 'pem',
          cipher: 'aes-256-cbc',
          passphrase: this.keyEncryptionPassword
        }
      }, (err, publicKey, privateKey) => {
        if (err) {
          logger.error('Key generation failed', err);
          reject(err);
        } else {
          logger.info('RSA key pair generated successfully', { keySize });
          resolve({ publicKey, privateKey });
        }
      });
    });
  }

  /**
   * Import public key from PEM string
   */
  importPublicKey(pemString) {
    try {
      const publicKey = crypto.createPublicKey(pemString);
      return publicKey.export({ type: 'spki', format: 'pem' });
    } catch (error) {
      logger.error('Failed to import public key', error);
      throw new Error('Invalid public key format');
    }
  }

  /**
   * Import private key from PEM string
   */
  importPrivateKey(pemString, passphrase = null) {
    try {
      const privateKey = crypto.createPrivateKey({
        key: pemString,
        passphrase: passphrase || this.keyEncryptionPassword
      });
      return privateKey.export({ 
        type: 'pkcs8', 
        format: 'pem',
        cipher: 'aes-256-cbc',
        passphrase: this.keyEncryptionPassword
      });
    } catch (error) {
      logger.error('Failed to import private key', error);
      throw new Error('Invalid private key format or passphrase');
    }
  }

  /**
   * Encrypt data with RSA public key
   */
  encryptWithPublicKey(data, publicKeyPem) {
    try {
      const buffer = Buffer.from(data, 'utf8');
      const encrypted = crypto.publicEncrypt({
        key: publicKeyPem,
        padding: crypto.constants.RSA_PKCS1_OAEP_PADDING,
        oaepHash: 'sha256'
      }, buffer);
      
      return encrypted.toString('base64');
    } catch (error) {
      logger.error('RSA encryption failed', error);
      throw new Error('Encryption failed');
    }
  }

  /**
   * Decrypt data with RSA private key
   */
  decryptWithPrivateKey(encryptedData, privateKeyPem, passphrase = null) {
    try {
      const buffer = Buffer.from(encryptedData, 'base64');
      const decrypted = crypto.privateDecrypt({
        key: privateKeyPem,
        passphrase: passphrase || this.keyEncryptionPassword,
        padding: crypto.constants.RSA_PKCS1_OAEP_PADDING,
        oaepHash: 'sha256'
      }, buffer);
      
      return decrypted.toString('utf8');
    } catch (error) {
      logger.error('RSA decryption failed', error);
      throw new Error('Decryption failed');
    }
  }

  /**
   * Save key pair to secure storage
   */
  async saveKeyPair(keyId, publicKey, privateKey) {
    try {
      const publicKeyPath = path.join(this.keyStoragePath, `${keyId}_public.pem`);
      const privateKeyPath = path.join(this.keyStoragePath, `${keyId}_private.pem`);
      
      await fs.writeFile(publicKeyPath, publicKey, { mode: 0o600 });
      await fs.writeFile(privateKeyPath, privateKey, { mode: 0o600 });
      
      logger.info('Key pair saved successfully', { keyId });
      return { publicKeyPath, privateKeyPath };
    } catch (error) {
      logger.error('Failed to save key pair', error);
      throw new Error('Key storage failed');
    }
  }

  /**
   * Load key pair from storage
   */
  async loadKeyPair(keyId) {
    try {
      const publicKeyPath = path.join(this.keyStoragePath, `${keyId}_public.pem`);
      const privateKeyPath = path.join(this.keyStoragePath, `${keyId}_private.pem`);
      
      const publicKey = await fs.readFile(publicKeyPath, 'utf8');
      const privateKey = await fs.readFile(privateKeyPath, 'utf8');
      
      return { publicKey, privateKey };
    } catch (error) {
      logger.error('Failed to load key pair', error);
      throw new Error('Key not found or inaccessible');
    }
  }

  /**
   * Delete key pair from storage
   */
  async deleteKeyPair(keyId) {
    try {
      const publicKeyPath = path.join(this.keyStoragePath, `${keyId}_public.pem`);
      const privateKeyPath = path.join(this.keyStoragePath, `${keyId}_private.pem`);
      
      await fs.unlink(publicKeyPath);
      await fs.unlink(privateKeyPath);
      
      logger.info('Key pair deleted successfully', { keyId });
    } catch (error) {
      logger.error('Failed to delete key pair', error);
      throw new Error('Key deletion failed');
    }
  }

  /**
   * List stored key pairs
   */
  async listKeyPairs() {
    try {
      const files = await fs.readdir(this.keyStoragePath);
      const keyIds = new Set();
      
      files.forEach(file => {
        if (file.endsWith('_public.pem')) {
          keyIds.add(file.replace('_public.pem', ''));
        }
      });
      
      return Array.from(keyIds);
    } catch (error) {
      logger.error('Failed to list key pairs', error);
      return [];
    }
  }

  /**
   * Validate key pair integrity
   */
  async validateKeyPair(publicKey, privateKey, passphrase = null) {
    try {
      const testData = 'test-validation-data';
      const encrypted = this.encryptWithPublicKey(testData, publicKey);
      const decrypted = this.decryptWithPrivateKey(encrypted, privateKey, passphrase);
      
      return decrypted === testData;
    } catch (error) {
      logger.error('Key pair validation failed', error);
      return false;
    }
  }
}

/**
 * AES Encryption Utilities
 */
class AESManager {
  /**
   * Generate AES key
   */
  generateKey(keySize = 256) {
    const keySizeBytes = keySize / 8;
    return crypto.randomBytes(keySizeBytes).toString('base64');
  }

  /**
   * Encrypt data with AES-256-GCM
   */
  encrypt(data, key) {
    try {
      const algorithm = 'aes-256-gcm';
      const keyBuffer = Buffer.from(key, 'base64');
      const iv = crypto.randomBytes(16);
      
      const cipher = crypto.createCipher(algorithm, keyBuffer);
      cipher.setAAD(Buffer.from('fuse19-encryption-service'));
      
      let encrypted = cipher.update(data, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      const authTag = cipher.getAuthTag();
      
      return {
        encrypted,
        iv: iv.toString('hex'),
        authTag: authTag.toString('hex')
      };
    } catch (error) {
      logger.error('AES encryption failed', error);
      throw new Error('AES encryption failed');
    }
  }

  /**
   * Decrypt data with AES-256-GCM
   */
  decrypt(encryptedData, key) {
    try {
      const algorithm = 'aes-256-gcm';
      const keyBuffer = Buffer.from(key, 'base64');
      
      const decipher = crypto.createDecipher(algorithm, keyBuffer);
      decipher.setAAD(Buffer.from('fuse19-encryption-service'));
      decipher.setAuthTag(Buffer.from(encryptedData.authTag, 'hex'));
      
      let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      
      return decrypted;
    } catch (error) {
      logger.error('AES decryption failed', error);
      throw new Error('AES decryption failed');
    }
  }
}

module.exports = {
  RSAKeyManager,
  AESManager
};
