const winston = require('winston');
const path = require('path');
const fs = require('fs');

// Ensure logs directory exists
const logsDir = path.join(__dirname, '../logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

const createLogger = () => {
  const logLevel = process.env.LOG_LEVEL || 'info';
  const logFile = process.env.LOG_FILE || path.join(logsDir, 'encryption-service.log');

  const logger = winston.createLogger({
    level: logLevel,
    format: winston.format.combine(
      winston.format.timestamp(),
      winston.format.errors({ stack: true }),
      winston.format.json()
    ),
    defaultMeta: { 
      service: 'encryption-service',
      version: process.env.SERVICE_VERSION || '1.0.0'
    },
    transports: [
      // Write all logs to file
      new winston.transports.File({ 
        filename: logFile,
        maxsize: 5242880, // 5MB
        maxFiles: 5,
        tailable: true
      }),
      
      // Write errors to separate file
      new winston.transports.File({ 
        filename: path.join(logsDir, 'error.log'),
        level: 'error',
        maxsize: 5242880, // 5MB
        maxFiles: 5,
        tailable: true
      })
    ],
  });

  // Add console transport for development
  if (process.env.NODE_ENV !== 'production') {
    logger.add(new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    }));
  }

  return logger;
};

module.exports = { createLogger };
