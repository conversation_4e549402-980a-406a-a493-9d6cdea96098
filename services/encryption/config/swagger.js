const swaggerJsdoc = require('swagger-jsdoc');

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'Fuse19 Encryption Service API',
      version: '1.0.0',
      description: 'A comprehensive encryption microservice providing RSA/AES encryption, key management, and digital signatures',
      contact: {
        name: 'Fuse19 Team',
        email: '<EMAIL>'
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT'
      }
    },
    servers: [
      {
        url: 'http://localhost:3001',
        description: 'Development server'
      },
      {
        url: 'https://encryption-api.fuse19.com',
        description: 'Production server'
      }
    ],
    components: {
      securitySchemes: {
        ApiKeyAuth: {
          type: 'apiKey',
          in: 'header',
          name: 'X-API-Key',
          description: 'API key for authentication. Format: fuse_<timestamp>_<signature>'
        },
        BearerAuth: {
          type: 'http',
          scheme: 'bearer',
          description: 'Bearer token authentication'
        }
      },
      schemas: {
        Error: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              example: false
            },
            message: {
              type: 'string',
              example: 'An error occurred'
            },
            code: {
              type: 'string',
              example: 'ERROR_CODE'
            },
            errors: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  field: {
                    type: 'string'
                  },
                  message: {
                    type: 'string'
                  }
                }
              }
            }
          }
        },
        KeyPair: {
          type: 'object',
          properties: {
            keyId: {
              type: 'string',
              example: 'key-123e4567-e89b-12d3-a456-426614174000'
            },
            publicKey: {
              type: 'string',
              example: '-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...\n-----END PUBLIC KEY-----'
            },
            privateKey: {
              type: 'string',
              example: '-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...\n-----END PRIVATE KEY-----'
            }
          }
        },
        EncryptionResult: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              example: true
            },
            encryptedData: {
              type: 'string',
              example: 'base64-encoded-encrypted-data'
            },
            algorithm: {
              type: 'string',
              example: 'RSA-OAEP-SHA256'
            },
            keyId: {
              type: 'string',
              nullable: true,
              example: 'key-123e4567-e89b-12d3-a456-426614174000'
            }
          }
        },
        SignatureResult: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              example: true
            },
            signature: {
              type: 'string',
              example: 'base64-encoded-signature'
            },
            hash: {
              type: 'string',
              nullable: true,
              example: 'sha256-hash-of-data'
            },
            algorithm: {
              type: 'string',
              example: 'RSA-PSS-SHA256'
            },
            keyId: {
              type: 'string',
              nullable: true,
              example: 'key-123e4567-e89b-12d3-a456-426614174000'
            },
            timestamp: {
              type: 'string',
              format: 'date-time',
              example: '2024-01-01T12:00:00.000Z'
            }
          }
        }
      },
      responses: {
        BadRequest: {
          description: 'Bad request - validation failed',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Error'
              }
            }
          }
        },
        Unauthorized: {
          description: 'Unauthorized - invalid or missing API key',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Error'
              }
            }
          }
        },
        InternalError: {
          description: 'Internal server error',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Error'
              }
            }
          }
        }
      }
    },
    security: [
      {
        ApiKeyAuth: []
      }
    ],
    tags: [
      {
        name: 'Key Management',
        description: 'RSA key pair generation, import, export, and storage'
      },
      {
        name: 'RSA Encryption',
        description: 'RSA encryption and decryption operations'
      },
      {
        name: 'AES Encryption',
        description: 'AES symmetric encryption and decryption'
      },
      {
        name: 'Hybrid Encryption',
        description: 'Combined AES + RSA encryption for large data'
      },
      {
        name: 'Digital Signatures',
        description: 'Digital signing and signature verification'
      },
      {
        name: 'Health',
        description: 'Service health and monitoring endpoints'
      }
    ]
  },
  apis: [
    './routes/*.js',
    './server.js'
  ]
};

const specs = swaggerJsdoc(options);

module.exports = specs;
