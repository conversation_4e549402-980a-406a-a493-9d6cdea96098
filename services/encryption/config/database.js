const mongoose = require('mongoose');
const { createLogger } = require('../utils/logger');

const logger = createLogger();

/**
 * Database connection configuration
 */
class DatabaseManager {
  constructor() {
    this.connection = null;
    this.isConnected = false;
  }

  /**
   * Connect to MongoDB
   */
  async connect() {
    try {
      const mongoUri = process.env.MONGO_URI || 'mongodb://localhost:27017/fuse19-encryption';
      
      const options = {
        maxPoolSize: 10,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000,
        bufferCommands: false,
        bufferMaxEntries: 0,
        retryWrites: true,
        w: 'majority'
      };

      logger.info('Connecting to MongoDB...', { uri: mongoUri.replace(/\/\/.*@/, '//***:***@') });

      this.connection = await mongoose.connect(mongoUri, options);
      this.isConnected = true;

      logger.info('MongoDB connected successfully', {
        host: this.connection.connection.host,
        port: this.connection.connection.port,
        database: this.connection.connection.name
      });

      // Handle connection events
      mongoose.connection.on('error', (error) => {
        logger.error('MongoDB connection error', error);
        this.isConnected = false;
      });

      mongoose.connection.on('disconnected', () => {
        logger.warn('MongoDB disconnected');
        this.isConnected = false;
      });

      mongoose.connection.on('reconnected', () => {
        logger.info('MongoDB reconnected');
        this.isConnected = true;
      });

      // Graceful shutdown
      process.on('SIGINT', async () => {
        await this.disconnect();
        process.exit(0);
      });

      process.on('SIGTERM', async () => {
        await this.disconnect();
        process.exit(0);
      });

      return this.connection;
    } catch (error) {
      logger.error('MongoDB connection failed', error);
      this.isConnected = false;
      throw error;
    }
  }

  /**
   * Disconnect from MongoDB
   */
  async disconnect() {
    try {
      if (this.connection) {
        await mongoose.connection.close();
        this.isConnected = false;
        logger.info('MongoDB disconnected gracefully');
      }
    } catch (error) {
      logger.error('Error disconnecting from MongoDB', error);
    }
  }

  /**
   * Check if database is connected
   */
  isHealthy() {
    return this.isConnected && mongoose.connection.readyState === 1;
  }

  /**
   * Get connection status
   */
  getStatus() {
    const states = {
      0: 'disconnected',
      1: 'connected',
      2: 'connecting',
      3: 'disconnecting'
    };

    return {
      isConnected: this.isConnected,
      readyState: mongoose.connection.readyState,
      status: states[mongoose.connection.readyState] || 'unknown',
      host: mongoose.connection.host,
      port: mongoose.connection.port,
      database: mongoose.connection.name
    };
  }

  /**
   * Create database indexes for performance
   */
  async createIndexes() {
    try {
      logger.info('Creating database indexes...');

      // Key metadata indexes
      await mongoose.connection.db.collection('keymetadata').createIndex({ keyId: 1 }, { unique: true });
      await mongoose.connection.db.collection('keymetadata').createIndex({ createdBy: 1 });
      await mongoose.connection.db.collection('keymetadata').createIndex({ status: 1 });
      await mongoose.connection.db.collection('keymetadata').createIndex({ expiresAt: 1 });
      await mongoose.connection.db.collection('keymetadata').createIndex({ createdAt: -1 });

      // Audit log indexes
      await mongoose.connection.db.collection('auditlogs').createIndex({ timestamp: -1 });
      await mongoose.connection.db.collection('auditlogs').createIndex({ action: 1 });
      await mongoose.connection.db.collection('auditlogs').createIndex({ clientId: 1 });
      await mongoose.connection.db.collection('auditlogs').createIndex({ keyId: 1 });
      await mongoose.connection.db.collection('auditlogs').createIndex({ ipAddress: 1 });

      // Client indexes
      await mongoose.connection.db.collection('clients').createIndex({ apiKey: 1 }, { unique: true });
      await mongoose.connection.db.collection('clients').createIndex({ clientId: 1 }, { unique: true });
      await mongoose.connection.db.collection('clients').createIndex({ status: 1 });
      await mongoose.connection.db.collection('clients').createIndex({ createdAt: -1 });

      // TTL index for expired audit logs (optional - keep logs for 1 year)
      await mongoose.connection.db.collection('auditlogs').createIndex(
        { timestamp: 1 }, 
        { expireAfterSeconds: 365 * 24 * 60 * 60 } // 1 year
      );

      logger.info('Database indexes created successfully');
    } catch (error) {
      logger.error('Failed to create database indexes', error);
      // Don't throw - indexes are for performance, not critical for functionality
    }
  }

  /**
   * Get database statistics
   */
  async getStats() {
    try {
      if (!this.isHealthy()) {
        return { error: 'Database not connected' };
      }

      const stats = await mongoose.connection.db.stats();
      const collections = await mongoose.connection.db.listCollections().toArray();

      return {
        database: mongoose.connection.name,
        collections: collections.length,
        dataSize: stats.dataSize,
        storageSize: stats.storageSize,
        indexes: stats.indexes,
        objects: stats.objects,
        avgObjSize: stats.avgObjSize
      };
    } catch (error) {
      logger.error('Failed to get database stats', error);
      return { error: error.message };
    }
  }
}

// Create singleton instance
const dbManager = new DatabaseManager();

module.exports = {
  DatabaseManager,
  dbManager,
  connectDB: () => dbManager.connect(),
  disconnectDB: () => dbManager.disconnect(),
  isDBHealthy: () => dbManager.isHealthy(),
  getDBStatus: () => dbManager.getStatus(),
  getDBStats: () => dbManager.getStats(),
  createDBIndexes: () => dbManager.createIndexes()
};
