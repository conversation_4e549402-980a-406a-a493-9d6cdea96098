version: '3.8'

services:
  encryption-service:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: fuse19-encryption-service-dev
    ports:
      - "3001:3001"
      - "9229:9229"  # Debug port
      - "9090:9090"  # Metrics port
    environment:
      - NODE_ENV=development
      - PORT=3001
      - SERVICE_SECRET=dev-service-secret
      - API_KEY_SECRET=dev-api-key-secret
      - REDIS_URL=redis://redis:6379
      - RATE_LIMIT_WINDOW_MS=900000
      - RATE_LIMIT_MAX_REQUESTS=1000  # Higher limit for development
      - ALLOWED_ORIGINS=http://localhost:4200,http://localhost:5000,http://localhost:3000
      - LOG_LEVEL=debug
      - KEY_STORAGE_PATH=/app/keys
      - KEY_ENCRYPTION_PASSWORD=dev-key-password
      - SERVICE_NAME=encryption-service
      - SERVICE_VERSION=1.0.0-dev
      - HEALTH_CHECK_INTERVAL=30000
      - METRICS_ENABLED=true
      - METRICS_PORT=9090
    volumes:
      - .:/app
      - /app/node_modules
      - encryption_keys_dev:/app/keys
      - encryption_logs_dev:/app/logs
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - encryption-network-dev
    command: ["npm", "run", "dev"]

  redis:
    image: redis:7-alpine
    container_name: fuse19-encryption-redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis_data_dev:/data
    restart: unless-stopped
    networks:
      - encryption-network-dev
    command: redis-server --appendonly yes

volumes:
  encryption_keys_dev:
    driver: local
  encryption_logs_dev:
    driver: local
  redis_data_dev:
    driver: local

networks:
  encryption-network-dev:
    driver: bridge
