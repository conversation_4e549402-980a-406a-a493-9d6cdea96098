# Development Dockerfile with hot reload
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Install system dependencies including nodemon
RUN apk add --no-cache \
    dumb-init \
    && rm -rf /var/cache/apk/*

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S encryption -u 1001

# Copy package files
COPY package*.json ./

# Install all dependencies (including dev dependencies)
RUN npm install && \
    npm cache clean --force

# Create necessary directories with proper permissions
RUN mkdir -p logs keys && \
    chown -R encryption:nodejs /app

# Switch to non-root user
USER encryption

# Expose ports
EXPOSE 3001 9229 9090

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD node -e "require('http').get('http://localhost:3001/health', (res) => { \
        process.exit(res.statusCode === 200 ? 0 : 1) \
    }).on('error', () => process.exit(1))"

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Start with nodemon for development
CMD ["npm", "run", "dev"]
