const mongoose = require('mongoose');

/**
 * Key Metadata Schema
 * Stores metadata about encryption keys without storing the actual key material
 */
const keyMetadataSchema = new mongoose.Schema({
  keyId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  
  keyType: {
    type: String,
    required: true,
    enum: ['RSA', 'AES', 'HYBRID'],
    index: true
  },
  
  keySize: {
    type: Number,
    required: true,
    validate: {
      validator: function(v) {
        if (this.keyType === 'RSA') {
          return [1024, 2048, 3072, 4096].includes(v);
        } else if (this.keyType === 'AES') {
          return [128, 192, 256].includes(v);
        }
        return true;
      },
      message: 'Invalid key size for the specified key type'
    }
  },
  
  algorithm: {
    type: String,
    required: true,
    enum: [
      'RSA-OAEP-SHA256',
      'RSA-PSS-SHA256', 
      'AES-256-GCM',
      'AES-192-GCM',
      'AES-128-GCM',
      'HYBRID-RSA-AES'
    ]
  },
  
  purpose: {
    type: String,
    required: true,
    enum: ['ENCRYPTION', 'SIGNING', 'BOTH'],
    default: 'ENCRYPTION'
  },
  
  status: {
    type: String,
    required: true,
    enum: ['ACTIVE', 'INACTIVE', 'REVOKED', 'EXPIRED', 'COMPROMISED'],
    default: 'ACTIVE',
    index: true
  },
  
  createdBy: {
    type: String,
    required: true,
    index: true
  },
  
  createdAt: {
    type: Date,
    default: Date.now,
    index: true
  },
  
  expiresAt: {
    type: Date,
    index: true,
    validate: {
      validator: function(v) {
        return !v || v > this.createdAt;
      },
      message: 'Expiration date must be after creation date'
    }
  },
  
  lastUsedAt: {
    type: Date,
    index: true
  },
  
  usageCount: {
    type: Number,
    default: 0,
    min: 0
  },
  
  maxUsageCount: {
    type: Number,
    min: 1,
    validate: {
      validator: function(v) {
        return !v || v > this.usageCount;
      },
      message: 'Max usage count must be greater than current usage count'
    }
  },
  
  // Storage location information (not the actual key)
  storageLocation: {
    type: String,
    required: true,
    enum: ['FILE_SYSTEM', 'DATABASE', 'HSM', 'CLOUD_KMS']
  },
  
  storagePath: {
    type: String,
    required: function() {
      return this.storageLocation === 'FILE_SYSTEM';
    }
  },
  
  // Key fingerprint for verification (SHA256 of public key)
  fingerprint: {
    type: String,
    required: true,
    unique: true,
    match: /^[a-f0-9]{64}$/i
  },
  
  // Permissions and access control
  permissions: {
    canEncrypt: {
      type: Boolean,
      default: true
    },
    canDecrypt: {
      type: Boolean,
      default: true
    },
    canSign: {
      type: Boolean,
      default: false
    },
    canVerify: {
      type: Boolean,
      default: true
    }
  },
  
  // Client/application that owns this key
  ownerId: {
    type: String,
    required: true,
    index: true
  },
  
  // Tags for organization and searching
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }],
  
  // Additional metadata
  metadata: {
    description: String,
    environment: {
      type: String,
      enum: ['development', 'staging', 'production'],
      default: 'development'
    },
    application: String,
    version: String,
    rotationSchedule: {
      type: String,
      enum: ['NEVER', 'MONTHLY', 'QUARTERLY', 'YEARLY']
    },
    nextRotationDate: Date
  },
  
  // Audit trail
  auditTrail: [{
    action: {
      type: String,
      required: true,
      enum: ['CREATED', 'ACTIVATED', 'DEACTIVATED', 'REVOKED', 'EXPIRED', 'USED', 'ROTATED']
    },
    timestamp: {
      type: Date,
      default: Date.now
    },
    performedBy: String,
    reason: String,
    ipAddress: String
  }],
  
  // Backup and recovery information
  backup: {
    isBackedUp: {
      type: Boolean,
      default: false
    },
    backupLocation: String,
    lastBackupAt: Date,
    recoveryKeyId: String
  }
}, {
  timestamps: true,
  collection: 'keymetadata'
});

// Indexes for performance
keyMetadataSchema.index({ createdBy: 1, status: 1 });
keyMetadataSchema.index({ ownerId: 1, keyType: 1 });
keyMetadataSchema.index({ tags: 1 });
keyMetadataSchema.index({ 'metadata.environment': 1 });
keyMetadataSchema.index({ expiresAt: 1 }, { sparse: true });

// Virtual for checking if key is expired
keyMetadataSchema.virtual('isExpired').get(function() {
  return this.expiresAt && this.expiresAt < new Date();
});

// Virtual for checking if key is active and usable
keyMetadataSchema.virtual('isUsable').get(function() {
  return this.status === 'ACTIVE' && !this.isExpired && 
         (!this.maxUsageCount || this.usageCount < this.maxUsageCount);
});

// Methods
keyMetadataSchema.methods.incrementUsage = function() {
  this.usageCount += 1;
  this.lastUsedAt = new Date();
  return this.save();
};

keyMetadataSchema.methods.addAuditEntry = function(action, performedBy, reason, ipAddress) {
  this.auditTrail.push({
    action,
    performedBy,
    reason,
    ipAddress,
    timestamp: new Date()
  });
  return this.save();
};

keyMetadataSchema.methods.revoke = function(performedBy, reason, ipAddress) {
  this.status = 'REVOKED';
  return this.addAuditEntry('REVOKED', performedBy, reason, ipAddress);
};

keyMetadataSchema.methods.activate = function(performedBy, ipAddress) {
  this.status = 'ACTIVE';
  return this.addAuditEntry('ACTIVATED', performedBy, null, ipAddress);
};

keyMetadataSchema.methods.deactivate = function(performedBy, reason, ipAddress) {
  this.status = 'INACTIVE';
  return this.addAuditEntry('DEACTIVATED', performedBy, reason, ipAddress);
};

// Static methods
keyMetadataSchema.statics.findActiveKeys = function(ownerId) {
  return this.find({ 
    ownerId, 
    status: 'ACTIVE',
    $or: [
      { expiresAt: { $exists: false } },
      { expiresAt: { $gt: new Date() } }
    ]
  });
};

keyMetadataSchema.statics.findExpiredKeys = function() {
  return this.find({
    status: { $ne: 'EXPIRED' },
    expiresAt: { $lt: new Date() }
  });
};

keyMetadataSchema.statics.getUsageStats = function(ownerId) {
  return this.aggregate([
    { $match: { ownerId } },
    {
      $group: {
        _id: '$keyType',
        count: { $sum: 1 },
        totalUsage: { $sum: '$usageCount' },
        activeCount: {
          $sum: {
            $cond: [{ $eq: ['$status', 'ACTIVE'] }, 1, 0]
          }
        }
      }
    }
  ]);
};

// Pre-save middleware
keyMetadataSchema.pre('save', function(next) {
  // Auto-expire keys if they've reached max usage
  if (this.maxUsageCount && this.usageCount >= this.maxUsageCount && this.status === 'ACTIVE') {
    this.status = 'EXPIRED';
    this.auditTrail.push({
      action: 'EXPIRED',
      timestamp: new Date(),
      reason: 'Maximum usage count reached'
    });
  }
  
  // Auto-expire keys based on expiration date
  if (this.expiresAt && this.expiresAt < new Date() && this.status === 'ACTIVE') {
    this.status = 'EXPIRED';
    this.auditTrail.push({
      action: 'EXPIRED',
      timestamp: new Date(),
      reason: 'Expiration date reached'
    });
  }
  
  next();
});

module.exports = mongoose.model('KeyMetadata', keyMetadataSchema);
