{"config": {"configFile": "/home/<USER>/Documents/Source/Fuse19-Backend/playwright.config.js", "rootDir": "/home/<USER>/Documents/Source/Fuse19-Backend/tests/e2e/specs", "forbidOnly": false, "fullyParallel": false, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 1}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "test-results/html-report"}], ["json", {"outputFile": "test-results/results.json"}], ["list", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/home/<USER>/Documents/Source/Fuse19-Backend/test-results/artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "chromium", "name": "chromium", "testDir": "/home/<USER>/Documents/Source/Fuse19-Backend/tests/e2e/specs", "testIgnore": [], "testMatch": ["backend-api.spec.js"], "timeout": 60000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.54.2", "workers": 1, "webServer": null}, "suites": [{"title": "backend-api.spec.js", "file": "backend-api.spec.js", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Backend API Tests", "file": "backend-api.spec.js", "line": 3, "column": 6, "specs": [], "suites": [{"title": "Authentication API", "file": "backend-api.spec.js", "line": 22, "column": 8, "specs": [{"title": "should login successfully", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 64, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T17:25:10.553Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "16211e0d4fe6a7765f78-ffeb210787dadb4e5680", "file": "backend-api.spec.js", "line": 23, "column": 5}, {"title": "should reject invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 12, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T17:25:10.729Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "16211e0d4fe6a7765f78-89c882acbe31709529a0", "file": "backend-api.spec.js", "line": 41, "column": 5}, {"title": "should get current user profile", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 11, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T17:25:10.744Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "16211e0d4fe6a7765f78-bae75a835c788e34faaa", "file": "backend-api.spec.js", "line": 54, "column": 5}]}, {"title": "Dashboard API", "file": "backend-api.spec.js", "line": 70, "column": 8, "specs": [{"title": "should get project dashboard data", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 9, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T17:25:10.757Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "16211e0d4fe6a7765f78-f4f0993c0dcfd64e3024", "file": "backend-api.spec.js", "line": 71, "column": 5}, {"title": "should get analytics dashboard data", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 8, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T17:25:10.768Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "16211e0d4fe6a7765f78-2ad57eb8c550898543ae", "file": "backend-api.spec.js", "line": 83, "column": 5}, {"title": "should get finance dashboard data", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 7, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T17:25:10.778Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "16211e0d4fe6a7765f78-d75f13670a4d8dd93ba1", "file": "backend-api.spec.js", "line": 93, "column": 5}, {"title": "should get crypto dashboard data", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 8, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T17:25:10.788Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "16211e0d4fe6a7765f78-71f6f4c3ee47535b88dd", "file": "backend-api.spec.js", "line": 107, "column": 5}]}, {"title": "Navigation API", "file": "backend-api.spec.js", "line": 122, "column": 8, "specs": [{"title": "should get navigation structure", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 11, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T17:25:10.798Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "16211e0d4fe6a7765f78-307a2677540e6be731d1", "file": "backend-api.spec.js", "line": 123, "column": 5}]}, {"title": "Application APIs", "file": "backend-api.spec.js", "line": 147, "column": 8, "specs": [{"title": "should get scrumboard boards", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 7, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T17:25:10.811Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "16211e0d4fe6a7765f78-70099c90991fb850a8b1", "file": "backend-api.spec.js", "line": 148, "column": 5}, {"title": "should get ecommerce inventory", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 8, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T17:25:10.820Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "16211e0d4fe6a7765f78-ddf70b62efe56f166b60", "file": "backend-api.spec.js", "line": 162, "column": 5}, {"title": "should get contacts list", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 9, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T17:25:10.831Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "16211e0d4fe6a7765f78-54cb315b9f4baea1b378", "file": "backend-api.spec.js", "line": 177, "column": 5}, {"title": "should get notes list", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 8, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T17:25:10.841Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "16211e0d4fe6a7765f78-86d15a0bef673d32499b", "file": "backend-api.spec.js", "line": 192, "column": 5}, {"title": "should get tasks list", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 8, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T17:25:10.851Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "16211e0d4fe6a7765f78-709f4dbc269d5b6049bc", "file": "backend-api.spec.js", "line": 207, "column": 5}, {"title": "should get chat conversations", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 9, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T17:25:10.861Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "16211e0d4fe6a7765f78-2e687eaf2531aa6083eb", "file": "backend-api.spec.js", "line": 222, "column": 5}]}, {"title": "Common APIs", "file": "backend-api.spec.js", "line": 238, "column": 8, "specs": [{"title": "should get messages", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 5, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T17:25:10.871Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "16211e0d4fe6a7765f78-a31517cd96659e419611", "file": "backend-api.spec.js", "line": 239, "column": 5}, {"title": "should get notifications", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 4, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T17:25:10.878Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "16211e0d4fe6a7765f78-94c349a8e74eb8f453dd", "file": "backend-api.spec.js", "line": 248, "column": 5}, {"title": "should get shortcuts", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 4, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T17:25:10.884Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "16211e0d4fe6a7765f78-ef7220afb22472d97b48", "file": "backend-api.spec.js", "line": 257, "column": 5}]}, {"title": "Health Check", "file": "backend-api.spec.js", "line": 267, "column": 8, "specs": [{"title": "should return healthy status", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 4, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-02T17:25:10.890Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "16211e0d4fe6a7765f78-d66884f8982cbc048bd0", "file": "backend-api.spec.js", "line": 268, "column": 5}]}]}]}], "errors": [], "stats": {"startTime": "2025-08-02T17:25:10.130Z", "duration": 777.1709999999999, "expected": 18, "skipped": 0, "unexpected": 0, "flaky": 0}}